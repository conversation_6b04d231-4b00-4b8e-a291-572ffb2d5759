<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini 绘本生成器</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <script src="{{ url_for('static', filename='js/table_logger.js') }}"></script>
</head>
<body>
    <div class="container">
        <h1>Gemini 绘本生成器</h1>

        <div class="form-container">
            <form id="promptForm">
                <div class="model-selection">
                    <label>选择模型:</label>
                    <div class="radio-group">
                        <label>
                            <input type="radio" name="model" value="{{ gemini_flash_model }}" checked>
                            {{ gemini_flash_model }}
                        </label>
                        <label>
                            <input type="radio" name="model" value="{{ gemini_image_model }}">
                            {{ gemini_image_model }}
                        </label>
                    </div>
                </div>
                <div>
                    <label for="prompt">输入提示词:</label>
                    <div id="prompt" name="prompt" contenteditable="true" class="rich-text-editor" style="min-height:120px;border:1px solid #ccc;padding:8px;border-radius:4px;background:#fff;outline:none;" required>{{ default_prompt|safe }}</div>
                </div>

                <div class="button-container">
                    <button type="submit" id="generateBtn" class="generate-btn">生成绘本</button>
                    <button type="button" id="testApiBtn" class="test-btn">测试API连接</button>
                </div>

                <div id="apiInfo">使用API密钥: {{ api_key }}</div>
            </form>

            <div id="statusMessage" class="status"></div>
        </div>

        <div class="loading" id="loadingIndicator">
            <div class="loading-spinner"></div>
            <p>正在处理请求，请稍候...</p>
        </div>

        <div class="result-container" id="resultContainer" style="display: none;">
            <h2>生成结果</h2>
            <div id="results"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('promptForm');
            const generateBtn = document.getElementById('generateBtn');
            const testApiBtn = document.getElementById('testApiBtn');
            const promptInput = document.getElementById('prompt');
            const results = document.getElementById('results');
            const resultContainer = document.getElementById('resultContainer');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const statusMessage = document.getElementById('statusMessage');
            const apiInfo = document.getElementById('apiInfo');

            // 项目保存、加载和管理按钮已移除

            // 生成绘本
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                console.log('生成按钮被点击');

                const prompt = promptInput.innerHTML.trim();
                if (!prompt) {
                    console.log('提示词为空，终止请求');
                    return;
                }

                // 重置UI
                results.innerHTML = '';
                resultContainer.style.display = 'none';
                statusMessage.className = 'status';
                statusMessage.style.display = 'none';
                loadingIndicator.style.display = 'block';
                generateBtn.disabled = true;

                try {
                    // 获取选中的模型
                    const selectedModel = document.querySelector('input[name="model"]:checked').value;
                    console.log('发送提示词:', prompt, '模型:', selectedModel);
                    const jsonData = JSON.stringify({ prompt: prompt, model: selectedModel });
                    console.log('JSON数据:', jsonData);

                    console.log('开始发送网络请求...');
                    const response = await fetch('/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: jsonData
                    });
                    console.log('网络请求已发送，状态码:', response.status);

                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态: ${response.status}`);
                    }

                    let data;
                    try {
                        data = await response.json();
                        console.log('收到API响应:', data);
                    } catch (jsonError) {
                        console.error('JSON解析错误:', jsonError);
                        throw new Error('无法将响应解析为JSON');
                    }

                    // 显示结果
                    resultContainer.style.display = 'block';

                    // 处理不同的状态类型
                    if (data.status === 'success' || data.status === 'warning') {
                        if (data.status === 'success') {
                            statusMessage.className = 'status success';
                            statusMessage.textContent = '生成成功！';
                        } else {
                            statusMessage.className = 'status warning';
                            statusMessage.textContent = `警告: ${data.message || '部分内容可能未正确生成'}`;
                        }
                        statusMessage.style.display = 'block';

                        // 处理API密钥信息
                        if (data.api_key) {
                            apiInfo.textContent = `使用API密钥: ${data.api_key}`;
                        }

                        // 处理结果
                        if (data.results && data.results.length > 0) {
                            data.results.forEach((item, index) => {
                                console.log(`处理结果项 ${index}:`, item);

                                // 为此结果创建容器
                                const resultItem = document.createElement('div');
                                resultItem.className = 'result-item';

                                // 处理图像
                                if (item.type === 'image' && item.data) {
                                    const imageContainer = document.createElement('div');
                                    imageContainer.className = 'image-container';

                                    try {
                                        const img = document.createElement('img');
                                        img.src = item.data;
                                        img.alt = `生成的图片 #${index+1}`;
                                        img.className = 'generated-image';
                                        img.onerror = function() {
                                            imageContainer.innerHTML = '<p class="error-message">图片加载失败</p>';
                                        };
                                        imageContainer.appendChild(img);
                                    } catch (imgError) {
                                        console.error('图片加载错误:', imgError);
                                        imageContainer.innerHTML = '<p class="error-message">图片处理失败</p>';
                                    }

                                    resultItem.appendChild(imageContainer);
                                }

                                // 处理文本
                                if (item.type === 'text' && item.content) {
                                    // 1. 创建LLM原始HTML显示容器，确保完整显示
                                    const llmContentWrapper = document.createElement('div');
                                    llmContentWrapper.className = 'llm-content-wrapper';

                                    // 2. 原始HTML内容显示区域
                                    const rawHtmlDiv = document.createElement('div');
                                    rawHtmlDiv.className = 'llm-raw-html';
                                    rawHtmlDiv.innerHTML = item.content;
                                    llmContentWrapper.appendChild(rawHtmlDiv);

                                    // 3. 功能按钮区域
                                    const functionsDiv = document.createElement('div');
                                    functionsDiv.className = 'llm-functions';

                                    // 4. 隐藏的富文本编辑区（用于后续功能处理）
                                    const richEditorDiv = document.createElement('div');
                                    richEditorDiv.className = 'rich-editor-hidden';
                                    richEditorDiv.style.display = 'none';
                                    richEditorDiv.innerHTML = item.content;

                                    // 5. 组装结构
                                    resultItem.appendChild(llmContentWrapper);
                                    resultItem.appendChild(functionsDiv);
                                    resultItem.appendChild(richEditorDiv);
                                    results.appendChild(resultItem);

                                    // 6. 插入"生成图片提示词表格"按钮到功能区域
                                    if (!document.querySelector('.gen-prompt-table-btn')) {
                                        const genPromptTableBtn = document.createElement('button');
                                        genPromptTableBtn.className = 'gen-prompt-table-btn';
                                        genPromptTableBtn.textContent = '生成图片提示词表格';
                                        genPromptTableBtn.style.marginTop = '16px';
                                        genPromptTableBtn.style.background = '#2563eb';
                                        genPromptTableBtn.style.color = '#fff';
                                        genPromptTableBtn.style.border = 'none';
                                        genPromptTableBtn.style.padding = '8px 16px';
                                        genPromptTableBtn.style.borderRadius = '6px';
                                        genPromptTableBtn.style.cursor = 'pointer';
                                        functionsDiv.appendChild(genPromptTableBtn);

                                        genPromptTableBtn.onclick = function() {
                                            // 解析HTML，生成表格 - 优化版本，最大化兼容各种格式
                                            const html = richEditorDiv.innerHTML;
                                            console.log('开始解析HTML内容，长度:', html.length);
                                            
                                            // HTML解码函数
                                            function decodeHtml(html) {
                                                const txt = document.createElement('textarea');
                                                txt.innerHTML = html;
                                                return txt.value;
                                            }
                                            
                                            // 先解码HTML转义字符，然后去除HTML标签
                                            const decodedHtml = decodeHtml(html);
                                            const tempDiv = document.createElement('div');
                                            tempDiv.innerHTML = decodedHtml;
                                            const plainText = tempDiv.textContent || tempDiv.innerText || '';
                                            
                                            console.log('🔍 原始HTML长度:', html.length);
                                            console.log('🔍 解码后HTML:', decodedHtml.substring(0, 300) + '...');
                                            console.log('🔍 提取的纯文本内容:', plainText.substring(0, 500) + '...');
                                            
                                            const prompts = [];
                                            let pageNum = 1;
                                            let lastPage = '';
                                            
                                            // 按段落分割文本进行处理，使用更宽松的分割方式
                                            const paragraphs = plainText.split(/\n+/).filter(p => p.trim());
                                            console.log('📄 分割后的段落数量:', paragraphs.length);
                                            
                                            // 同时也尝试整体匹配，防止段落分割导致的问题
                                            const fullTextMatches = [];
                                            
                                            // 第一步：统计页码总数
                                            const pageMatches = plainText.match(/第([一二三四五六七八九十\d]+)页/g) || [];
                                            const totalPages = pageMatches.length;
                                            console.log('📊 检测到的总页数:', totalPages, '页码列表:', pageMatches);
                                            
                                            // 第二步：优先使用【】括号方式提取生图提示词
                                            const globalMatches = [
                                                ...plainText.matchAll(/[【]([\s\S]*?)[】]/g)
                                            ];
                                            
                                            console.log('🔍 全局【】括号匹配结果数量:', globalMatches.length);
                                            globalMatches.forEach((match, idx) => {
                                                console.log(`全局【】匹配${idx + 1}:`, match[1].substring(0, 100) + '...');
                                            });
                                            
                                            paragraphs.forEach((txt, index) => {
                                                txt = txt.trim();
                                                if (!txt) return;
                                                
                                                console.log(`\n=== 📝 处理第${index + 1}个段落 ===`);
                                                console.log('段落完整内容:', txt);
                                                
                                                // 提取页数信息
                                                let pageMatch = txt.match(/第([一二三四五六七八九十\d]+)页/);
                                                if (pageMatch) {
                                                    lastPage = pageMatch[0];
                                                    console.log('📖 找到页数:', lastPage);
                                                    return;
                                                }
                                                
                                                // 统一使用【】括号提取生图提示词
                                                let promptText = '';
                                                
                                                // 查找【】括号内的内容
                                                let bracketMatch = txt.match(/[【]([\s\S]*?)[】]/);
                                                if (bracketMatch) {
                                                    let content = bracketMatch[1].trim();
                                                    // 过滤掉明显不是生图提示词的内容
                                                    if (content.length > 15 && 
                                                        !content.includes('第') && 
                                                        !content.includes('页') &&
                                                        (content.includes('风格') || 
                                                         content.includes('画面') || 
                                                         content.includes('场景') ||
                                                         content.includes('图片') ||
                                                         content.includes('配图') ||
                                                         content.includes('生图') ||
                                                         content.length > 30)) {
                                                        promptText = content;
                                                        console.log('✅ 找到【】括号内容:', promptText);
                                                    }
                                                }
                                                
                                                // 如果找到了提示词内容，添加到数组中
                                                if (promptText) {
                                                    console.log('✅ 找到有效提示词:', promptText);
                                                    prompts.push({
                                                        index: prompts.length + 1,
                                                        page: lastPage || `第${pageNum}页`,
                                                        prompt: promptText
                                                    });
                                                    pageNum++;
                                                }
                                            });
                                            
                                            // 如果段落匹配没有找到足够的提示词，使用全局【】括号匹配结果作为补充
                                            if (prompts.length === 0 && globalMatches.length > 0) {
                                                console.log('🔄 段落匹配失败，使用全局【】括号匹配结果');
                                                globalMatches.forEach((match, idx) => {
                                                    let content = match[1].trim();
                                                    
                                                    // 过滤掉明显不是生图提示词的内容
                                                    if (content.length > 15 && 
                                                        !content.includes('第') && 
                                                        !content.includes('页') &&
                                                        (content.includes('风格') || 
                                                         content.includes('画面') || 
                                                         content.includes('场景') ||
                                                         content.includes('图片') ||
                                                         content.includes('配图') ||
                                                         content.includes('生图') ||
                                                         content.length > 30)) {
                                                        
                                                        console.log(`✅ 全局【】匹配${idx + 1}:`, content);
                                                        prompts.push({
                                                            index: prompts.length + 1,
                                                            page: `第${prompts.length + 1}页`,
                                                            prompt: content
                                                        });
                                                    }
                                                });
                                            }
                                            
                                            // 第三步：检查生图提示词数量是否与页码数匹配
                                            console.log('📊 【】括号提取结果 - 提示词数量:', prompts.length, '页码数量:', totalPages);
                                            
                                            // 如果【】括号提取的生图提示词数量与页码数不匹配，使用"生图Prompt详细描述："后的内容重新提取
                                            if (totalPages > 0 && prompts.length !== totalPages) {
                                                console.log('⚠️ 【】括号提取的生图提示词数量与页码数不匹配，启用"生图Prompt详细描述："提取模式');
                                                console.log('🔄 清空现有提示词数组，重新提取...');
                                                
                                                // 清空现有结果
                                                prompts.length = 0;
                                                pageNum = 1;
                                                lastPage = '';
                                                
                                                // 使用"生图Prompt详细描述："后的内容进行提取
                                                const promptDescMatches = [
                                                    // 格式1：生图Prompt详细描述：【内容】
                                                    ...plainText.matchAll(/生图Prompt详细描述[：:]\s*[【]([^】]+)[】]/g),
                                                    // 格式2：生图Prompt详细描述：内容（无括号，到句号或换行结束）
                                                    ...plainText.matchAll(/生图Prompt详细描述[：:]\s*([^。\n【】]+?)(?=[。\n]|$)/g),
                                                    // 格式3：场景配图，生图Prompt详细描述：【内容】
                                                    ...plainText.matchAll(/场景配图[，,]\s*生图Prompt详细描述[：:]\s*[【]?([^】\n]+)[】]?/g)
                                                ];
                                                
                                                console.log('🔍 "生图Prompt详细描述："匹配结果数量:', promptDescMatches.length);
                                                promptDescMatches.forEach((match, idx) => {
                                                    console.log(`"生图Prompt详细描述："匹配${idx + 1}:`, match[1].substring(0, 100) + '...');
                                                });
                                                
                                                // 重新按段落处理，结合页码信息
                                                paragraphs.forEach((txt, index) => {
                                                    txt = txt.trim();
                                                    if (!txt) return;
                                                    
                                                    // 提取页数信息
                                                    let pageMatch = txt.match(/第([一二三四五六七八九十\d]+)页/);
                                                    if (pageMatch) {
                                                        lastPage = pageMatch[0];
                                                        console.log('📖 重新找到页数:', lastPage);
                                                        return;
                                                    }
                                                    
                                                    // 查找"生图Prompt详细描述："后的内容
                                                    let promptText = '';
                                                    
                                                    // 尝试多种格式匹配
                                                    let promptMatch1 = txt.match(/生图Prompt详细描述[：:]\s*[【]([^】]+)[】]/);
                                                    if (promptMatch1) {
                                                        promptText = promptMatch1[1].trim();
                                                        console.log('✅ 找到格式1（带括号）:', promptText);
                                                    } else {
                                                        let promptMatch2 = txt.match(/生图Prompt详细描述[：:]\s*([^。\n【】]+?)(?=[。\n]|$)/);
                                                        if (promptMatch2) {
                                                            promptText = promptMatch2[1].trim();
                                                            console.log('✅ 找到格式2（无括号）:', promptText);
                                                        } else {
                                                            let promptMatch3 = txt.match(/场景配图[，,]\s*生图Prompt详细描述[：:]\s*[【]?([^】\n]+)[】]?/);
                                                            if (promptMatch3) {
                                                                promptText = promptMatch3[1].trim();
                                                                console.log('✅ 找到格式3（场景配图）:', promptText);
                                                            }
                                                        }
                                                    }
                                                    
                                                    // 如果找到了提示词内容，添加到数组中
                                                    if (promptText && promptText.length > 10) {
                                                        console.log('✅ 找到有效"生图Prompt详细描述："提示词:', promptText);
                                                        prompts.push({
                                                            index: prompts.length + 1,
                                                            page: lastPage || `第${pageNum}页`,
                                                            prompt: promptText
                                                        });
                                                        pageNum++;
                                                    }
                                                });
                                                
                                                // 如果段落匹配仍然不够，使用全局"生图Prompt详细描述："匹配结果补充
                                                if (prompts.length < totalPages && promptDescMatches.length > 0) {
                                                    console.log('🔄 段落"生图Prompt详细描述："匹配不足，使用全局匹配结果补充');
                                                    promptDescMatches.forEach((match, idx) => {
                                                        if (prompts.length >= totalPages) return; // 已达到页码数量，停止添加
                                                        
                                                        let content = match[1].trim();
                                                        
                                                        // 过滤掉明显不是生图提示词的内容
                                                        if (content.length > 10 && 
                                                            !content.includes('第') && 
                                                            !content.includes('页')) {
                                                            
                                                            console.log(`✅ 全局"生图Prompt详细描述："匹配${idx + 1}:`, content);
                                                            prompts.push({
                                                                index: prompts.length + 1,
                                                                page: `第${prompts.length + 1}页`,
                                                                prompt: content
                                                            });
                                                        }
                                                    });
                                                }
                                                
                                                console.log('📊 "生图Prompt详细描述："提取结果 - 提示词数量:', prompts.length, '目标页码数量:', totalPages);
                                            }
                                            
                                            console.log('📊 最终解析到的提示词数量:', prompts.length);
                                            console.log('📋 提示词列表:', prompts.map((p, i) => `${i+1}. ${p.prompt.substring(0, 50)}...`));
                                            
                                            // 如果没有找到提示词，显示详细的错误信息
                                            if (prompts.length === 0) {
                                                alert('未找到生图提示词！\n\n可能的原因：\n1. 内容中没有包含"生图Prompt详细描述"字样\n2. 格式不正确\n3. 内容被HTML标签包裹导致无法识别\n\n请检查生成的内容是否包含生图提示词。\n\n调试信息已输出到浏览器控制台，请按F12查看。');
                                                console.log('❌ 调试信息:');
                                                console.log('原始HTML:', html);
                                                console.log('纯文本内容:', plainText);
                                                console.log('分割的段落:', paragraphs);
                                                return;
                                            }
                                            let tableHtml = `<table class='prompt-table' style='width:100%;margin-top:16px;border-collapse:collapse;'>
                                                <thead>
                                                    <tr style='background:#f3f4f6;'>
                                                        <th style='border:1px solid #ddd;padding:6px;'>序号</th>
                                                        <th style='border:1px solid #ddd;padding:6px;'>页数</th>
                                                        <th style='border:1px solid #ddd;padding:6px;'>生图提示词</th>
                                                        <th style='border:1px solid #ddd;padding:6px;'>运镜提示词</th>
                                                        <th style='border:1px solid #ddd;padding:6px;'>A过渡B运镜提示词</th>
                                                        <th style='border:1px solid #ddd;padding:6px;'>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody>`;
                                            prompts.forEach(row => {
                                                tableHtml += `<tr>
                                                    <td style='border:1px solid #ddd;padding:6px;text-align:center;'>${row.index}</td>
                                                    <td style='border:1px solid #ddd;padding:6px;text-align:center;'>${row.page}</td>
                                                    <td style='border:1px solid #ddd;padding:6px;'>${row.prompt}</td>
                                                    <td style='border:1px solid #ddd;padding:6px;' id='camera-prompt-cell-${row.index-1}'>
                                                        <div style='text-align:center;color:#6b7280;'>
                                                            <button class='generate-camera-btn' data-index='${row.index-1}' style='background:#10b981;color:#fff;border:none;padding:4px 8px;border-radius:4px;cursor:pointer;font-size:12px;'>生成运镜</button>
                                                        </div>
                                                    </td>
                                                    <td style='border:1px solid #ddd;padding:6px;' id='transition-camera-prompt-cell-${row.index-1}'>
                                                        <div style='text-align:center;color:#6b7280;'>
                                                            <button class='generate-transition-camera-btn' data-index='${row.index-1}' style='background:#3b82f6;color:#fff;border:none;padding:4px 8px;border-radius:4px;cursor:pointer;font-size:12px;'>生成过渡运镜</button>
                                                        </div>
                                                    </td>
                                                    <td style='border:1px solid #ddd;padding:6px;text-align:center;'>
                                                        <a href='#' class='view-prompt-link' data-index='${row.index-1}'>查看</a>
                                                    </td>
                                                </tr>`;
                                            });
                                            
                                            // 添加汇总行
                                            tableHtml += `<tr style='background:#fef3c7;border-top:2px solid #f59e0b;'>
                                                <td style='border:1px solid #ddd;padding:6px;text-align:center;font-weight:bold;'>汇总</td>
                                                <td style='border:1px solid #ddd;padding:6px;text-align:center;font-weight:bold;'>全部</td>
                                                <td style='border:1px solid #ddd;padding:6px;' id='summary-prompt-cell'>
                                                    <div style='text-align:center;color:#6b7280;'>正在生成汇总提示词...</div>
                                                </td>
                                                <td style='border:1px solid #ddd;padding:6px;text-align:center;'>-</td>
                                                <td style='border:1px solid #ddd;padding:6px;text-align:center;'>-</td>
                                                <td style='border:1px solid #ddd;padding:6px;text-align:center;'>
                                                    <span style='color:#6b7280;'>-</span>
                                                </td>
                                                <td style='border:1px solid #ddd;padding:6px;text-align:center;'>
                                                    <a href='#' id='summary-view-link' style='color:#6b7280;pointer-events:none;'>查看</a>
                                                </td>
                                            </tr>`;
                                            
                                            tableHtml += '</tbody></table>';
                                            
                                            // 移除旧表格
                                            let oldTable = document.querySelector('.prompt-table');
                                            if (oldTable) oldTable.remove();
                                            
                                            // 创建新表格
                                            const tableDiv = document.createElement('div');
                                            tableDiv.innerHTML = tableHtml;
                                            functionsDiv.appendChild(tableDiv);
                                            
                                            // 生成汇总提示词
                                            generateSummaryPrompt(prompts);
                                            
                                            // 输出成功信息
                                            console.log('✅ 图片提示词表格生成成功！解析到', prompts.length, '个提示词');
                                            console.log('提示词详情:', prompts.map(p => `${p.page}: ${p.prompt.substring(0, 50)}...`));
                                            
                                            // 显示成功提示
                                            alert(`✅ 图片提示词表格生成成功！\n\n解析到 ${prompts.length} 个提示词\n\n详细信息请查看生成的表格。`);
                                            tableDiv.querySelectorAll('.view-prompt-link').forEach(link => {
                                                link.onclick = function(e) {
                                                    e.preventDefault();
                                                    const idx = parseInt(link.getAttribute('data-index'));
                                                    showPromptModal(prompts, idx, tableDiv);
                                                };
                                            });
                                            
                                            // 绑定运镜提示词生成按钮事件
                                            tableDiv.querySelectorAll('.generate-camera-btn').forEach(btn => {
                                                btn.onclick = function(e) {
                                                    e.preventDefault();
                                                    const idx = parseInt(btn.getAttribute('data-index'));
                                                    generateCameraPrompt(prompts[idx], idx, tableDiv);
                                                };
                                            });
                                            
                                            // 绑定过渡运镜提示词生成按钮事件
                                            tableDiv.querySelectorAll('.generate-transition-camera-btn').forEach(btn => {
                                                btn.onclick = function(e) {
                                                    e.preventDefault();
                                                    const idx = parseInt(btn.getAttribute('data-index'));
                                                    generateTransitionCameraPrompt(prompts, idx, tableDiv);
                                                };
                                            });
                                            
                                            // 生成运镜提示词的函数
                                            function generateCameraPrompt(promptData, index, tableDiv) {
                                                console.log('🎬 开始生成运镜提示词...', '页码:', index + 1, '提示词数据:', promptData);
                                                
                                                const btn = tableDiv.querySelector(`button[data-index='${index}']`);
                                                const cell = document.getElementById(`camera-prompt-cell-${index}`);
                                                
                                                // 检查按钮是否正在处理中，防止重复点击
                                                if (btn.disabled) {
                                                    console.log('⚠️ 运镜按钮正在处理中，忽略重复点击');
                                                    return;
                                                }
                                                
                                                // 更新按钮状态
                                                btn.disabled = true;
                                                btn.textContent = '生成中...';
                                                btn.style.background = '#6b7280';
                                                
                                                console.log('📝 运镜按钮状态已更新，开始生成流程...');
                                                
                                                // 构建运镜提示词的Prompt（使用后端配置）
                                                // 注意：实际的运镜提示词构建逻辑已移植到config.py中的build_camera_prompt函数
                                                // 前端只需要发送生图提示词内容，后端会自动构建完整的运镜提示词
                                                let cameraPrompt = promptData.prompt;
                                                
                                                console.log('📝 发送给后端的生图提示词内容:', cameraPrompt);
                                                
                                                // 直接调用后端API生成运镜提示词（纯文本模式）
                                                fetch('/generate', {
                                                    method: 'POST',
                                                    headers: {
                                                        'Content-Type': 'application/json',
                                                    },
                                                    body: JSON.stringify({
                                                        prompt: cameraPrompt,
                                                        model: '{{ gemini_flash_model }}',
                                                        request_type: 'camera_prompt_plain_text',
                                                        page_id: index + 1  // 传递当前页码ID（从1开始）
                                                    })
                                                })
                                                        .then(response => response.json())
                                                        .then(data => {
                                                            console.log('📨 运镜API响应:', data);
                                                            console.log('📊 运镜API响应状态:', data.status);
                                                            console.log('📋 运镜API响应结果数量:', data.results ? data.results.length : 0);
                                                            
                                                            if (data.status === 'success' && data.results && data.results.length > 0) {
                                                                // 直接使用API返回的纯文本内容
                                                                let cameraContent = data.results[0].content;
                                                                console.log('📝 原始运镜内容:', cameraContent);
                                                                
                                                                // 基本清理（去除多余空白字符）
                                                                cameraContent = cameraContent
                                                                    .replace(/[\r\n\t]+/g, ' ')
                                                                    .replace(/\s+/g, ' ')
                                                                    .trim();
                                                                console.log('🧹 清理后运镜内容:', cameraContent);
                                                                
                                                                // 添加固定前缀
                                                                const finalCameraPrompt = '以这张图片为首镜头，生成一个多机位视频，' + cameraContent;
                                                                console.log('🎯 最终运镜提示词:', finalCameraPrompt);
                                                                
                                                                // 等待Excel写入完成后再更新前端显示
                                                                console.log('⏳ 等待Excel写入完成，页码:', index + 1, '内容长度:', finalCameraPrompt.length);
                                                                setTimeout(() => {
                                                                    console.log('🔄 开始更新前端显示，页码:', index + 1);
                                                                    // 更新单元格内容
                                                                    cell.innerHTML = `<div style='font-size:12px;line-height:1.4;'>${finalCameraPrompt}</div>`;
                                                                    console.log('✅ 运镜提示词前端显示已更新，页码:', index + 1, '显示内容长度:', finalCameraPrompt.length);
                                                                }, 2000); // 等待2秒确保Excel写入完成
                                                                
                                                                console.log('✅ 运镜提示词生成成功，页码:', index + 1);
                                                            } else {
                                                                const errorMsg = data.message || '未知错误';
                                                                cell.innerHTML = `<div style="color:#ef4444;font-size:12px;">运镜生成失败：${errorMsg}</div>`;
                                                                console.error('❌ 运镜生成失败，页码:', index + 1, '错误:', errorMsg);
                                                            }
                                                            
                                                            // 延迟恢复按钮状态，确保操作完成
                                                            setTimeout(() => {
                                                                btn.disabled = false;
                                                                btn.textContent = '重新生成';
                                                                btn.style.background = '#10b981';
                                                                console.log('🔄 运镜按钮状态已恢复，页码:', index + 1);
                                                            }, 3000); // 3秒后恢复按钮，确保前端显示更新完成
                                                        })
                                                        .catch(error => {
                                                            console.error('❌ 运镜API调用失败，页码:', index + 1, '错误:', error);
                                                            cell.innerHTML = '<div style="color:#ef4444;font-size:12px;">运镜生成失败：网络错误</div>';
                                                            
                                                            // 恢复按钮状态
                                                            btn.disabled = false;
                                                            btn.textContent = '重新生成';
                                                            btn.style.background = '#10b981';
                                                            console.log('🔄 运镜按钮状态已恢复（网络错误），页码:', index + 1);
                                                        });
                                            }
                                            
                                            // 生成过渡运镜提示词的函数
                                            function generateTransitionCameraPrompt(prompts, index, tableDiv) {
                                                console.log('🎬 开始生成过渡运镜提示词...', '当前页码:', index + 1);
                                                
                                                const btn = tableDiv.querySelector(`.generate-transition-camera-btn[data-index='${index}']`);
                                                const cell = document.getElementById(`transition-camera-prompt-cell-${index}`);
                                                
                                                // 检查按钮是否正在处理中，防止重复点击
                                                if (btn.disabled) {
                                                    console.log('⚠️ 过渡运镜按钮正在处理中，忽略重复点击');
                                                    return;
                                                }
                                                
                                                // 检查是否有下一页
                                                if (index + 1 >= prompts.length) {
                                                    cell.innerHTML = '<div style="color:#ef4444;font-size:12px;">最后一页无法生成过渡运镜</div>';
                                                    return;
                                                }
                                                
                                                // 更新按钮状态
                                                btn.disabled = true;
                                                btn.textContent = '生成中...';
                                                btn.style.background = '#6b7280';
                                                
                                                console.log('📝 过渡运镜按钮状态已更新，开始生成流程...');
                                                
                                                // 获取下一页的生图提示词作为结束画面内容
                                                const nextPagePrompt = prompts[index + 1].prompt;
                                                console.log('📝 下一页生图提示词（原始）:', nextPagePrompt);
                                                
                                                // 先获取当前页的运镜提示词
                                                const currentCameraCell = document.getElementById(`camera-prompt-cell-${index}`);
                                                let currentCameraContent = '';
                                                
                                                if (currentCameraCell && currentCameraCell.textContent && currentCameraCell.textContent.trim() !== '' && !currentCameraCell.textContent.includes('生成运镜')) {
                                                    currentCameraContent = currentCameraCell.textContent.trim();
                                                    console.log('📝 当前页运镜提示词内容:', currentCameraContent);
                                                } else {
                                                    // 如果当前页运镜提示词未生成或为空，弹出消息窗口提示用户
                                                    alert('请先生成运镜提示词！');
                                                    
                                                    // 恢复按钮状态，按钮状态保持不变
                                                    btn.disabled = false;
                                                    btn.textContent = '生成过渡运镜';
                                                    btn.style.background = '#3b82f6';
                                                    
                                                    console.log('⚠️ 需要先生成当前页运镜提示词，已弹出提示窗口');
                                                    return;
                                                }
                                                
                                                // 第一步：通过LLM提取下一页生图提示词的核心故事内容作为结束画面
                                                const extractEndScenePrompt = `请从以下生图提示词中提取核心的故事内容描述，去除技术性描述和格式要求，只保留画面场景的核心内容：\n\n${nextPagePrompt}\n\n请直接输出提取的核心故事内容，不要任何其他回复。`;
                                                
                                                console.log('📝 第一步：提取结束画面内容的提示词:', extractEndScenePrompt);
                                                
                                                // 调用LLM提取结束画面内容
                                                fetch('/generate', {
                                                    method: 'POST',
                                                    headers: {
                                                        'Content-Type': 'application/json',
                                                    },
                                                    body: JSON.stringify({
                                                        prompt: extractEndScenePrompt,
                                                        model: '{{ gemini_flash_model }}',
                                                        request_type: 'plain_text'
                                                    })
                                                })
                                                .then(response => response.json())
                                                .then(endSceneData => {
                                                    console.log('📨 结束画面提取API响应:', endSceneData);
                                                    
                                                    if (endSceneData.status === 'success' && endSceneData.results && endSceneData.results.length > 0) {
                                                        const endSceneContent = endSceneData.results[0].content.trim();
                                                        console.log('📝 提取的结束画面内容:', endSceneContent);
                                                        
                                                        // 第二步：通过LLM提取当前页运镜提示词的最后一个场景作为开始画面
                                                        const extractStartScenePrompt = `请从以下运镜提示词中提取最后一个场景的画面内容描述，只保留画面场景的核心内容：\n\n${currentCameraContent}\n\n请直接输出最后一个场景的画面内容，不要任何其他回复。`;
                                                        
                                                        console.log('📝 第二步：提取开始画面内容的提示词:', extractStartScenePrompt);
                                                        
                                                        // 调用LLM提取开始画面内容
                                                        fetch('/generate', {
                                                            method: 'POST',
                                                            headers: {
                                                                'Content-Type': 'application/json',
                                                            },
                                                            body: JSON.stringify({
                                                                prompt: extractStartScenePrompt,
                                                                model: '{{ gemini_flash_model }}',
                                                                request_type: 'plain_text'
                                                            })
                                                        })
                                                        .then(response => response.json())
                                                        .then(startSceneData => {
                                                            console.log('📨 开始画面提取API响应:', startSceneData);
                                                            
                                                            if (startSceneData.status === 'success' && startSceneData.results && startSceneData.results.length > 0) {
                                                                const startSceneContent = startSceneData.results[0].content.trim();
                                                                console.log('📝 提取的开始画面内容:', startSceneContent);
                                                                
                                                                // 第三步：构建组合提示词并生成过渡运镜（参考config.py中build_camera_prompt的模板规范）
                                                 const transitionPrompt = `要求有2~3个连贯的分镜，分镜脚本结构为：景别，视角，运镜，画面内容（LLM消息先智能处理"生图提示词"，并取出中间描述故事内容的部分后，返回LLM消息内容），镜头切换固定硬切，用词精练不能起过300字。请直接输出返回内容，不要任何回复。

过渡场景内容：从${startSceneContent}合理过渡到${endSceneContent}的场景

即梦3.0运镜黄金公式：[主体]在[场景]做[动作] + [运镜类型]（[参数]） + [辅助元素] + [情绪强化]
案例：舞者（主体）在雨林（场景）旋转跳跃（动作） + 环绕运镜（半径2m/速度0.5x） + 雨滴附着丝带（辅助） + 心跳震颤（恐惧时120bpm）

=== 基础运镜 ===
推镜头(Zoom In)：镜头物理靠近主体，突出细节/制造压迫感
  应用场景：广告产品特写、人物情绪转折
  操作口诀：镜头从A推到B，聚焦[细节]，时长X秒

拉镜头(Zoom Out)：镜头物理远离主体，展现场景全貌/收尾
  应用场景：纪录片环境全景、电影结尾
  操作口诀：从C特写匀速拉远，展现[D场景]

摇镜头(Pan)：三脚架式水平/垂直转动，横向扫描环境
  应用场景：历史片建筑群、集市全貌
  操作口诀：左→右摇180°，速度X，拍[场景]

移镜头(Track)：沿轨迹滑行拍摄，动态探索空间
  应用场景：旅游vlog街景、追逐戏跟拍
  操作口诀：沿[路径]移动，速度X，拍[内容]

跟镜头(Follow)：与运动主体同步移动，强化动作连贯性
  应用场景：体育赛事、武侠打斗
  操作口诀：跟拍[主体]，保持距离X米

=== 创意运镜 ===
环绕运镜(Orbital)：圆周环绕主体，360°展示物体
  应用场景：汽车广告、角色亮相
  操作口诀：绕[主体]转X圈，半径X米

对冲运镜(Counter)：镜头与主体反向运动，制造速度与冲突
  应用场景：警匪追车戏、动作片
  操作口诀：镜头前冲 vs [主体]后撤

POV视角：第一人称视角，沉浸式体验
  应用场景：游戏宣传片、探险纪录片
  操作口诀：POV视角：我[动作]+[所见]

请根据以上运镜参考，思考不同场景下的运镜特效，给观众最舒适的画面体验，为过渡场景设计2~3个连贯的分镜脚本，
格式如下：
景别[1~N]：XXX；
视角：XXX；
运镜：XXX；
画面内容：XXX(要求用词精练，总字数不超过300字。)`;
                                                                 
                                                                 console.log('📝 第三步：发送给后端的过渡运镜提示词:', transitionPrompt);
                                                                 
                                                                 // 调用后端API生成过渡运镜提示词
                                                                 fetch('/generate', {
                                                                     method: 'POST',
                                                                     headers: {
                                                                         'Content-Type': 'application/json',
                                                                     },
                                                                     body: JSON.stringify({
                                                                         prompt: transitionPrompt,
                                                                         model: '{{ gemini_flash_model }}',
                                                                         request_type: 'transition_camera_prompt_plain_text',
                                                                         current_page_id: index + 1,
                                                                         next_page_id: index + 2,
                                                                         page_id: index + 1
                                                                     })
                                                                 })
                                                                 .then(response => response.json())
                                                                 .then(data => {
                                                                     console.log('📨 过渡运镜生成API响应:', data);
                                                                     
                                                                     if (data.status === 'success' && data.results && data.results.length > 0) {
                                                                         console.log('✅ 过渡运镜提示词生成成功，后端已写入PromptGuoduYunjing表格');
                                                                         
                                                                         // 后端已经写入Excel表格，现在从Excel读取已处理的内容来更新HTML表格
                                                                         // 调用后端API获取Excel中存储的过渡运镜提示词
                                                                         fetch('/get_transition_camera_prompt', {
                                                                             method: 'POST',
                                                                             headers: {
                                                                                 'Content-Type': 'application/json',
                                                                             },
                                                                             body: JSON.stringify({
                                                                                 page_id: index + 1
                                                                             })
                                                                         })
                                                                         .then(response => response.json())
                                                                         .then(excelData => {
                                                                             console.log('📨 从Excel获取过渡运镜内容响应:', excelData);
                                                                             
                                                                             if (excelData.status === 'success' && excelData.content) {
                                                                                 // 使用Excel中存储的已处理内容更新HTML表格
                                                                                 const processedContent = excelData.content.trim();
                                                                                 console.log('📝 从Excel获取的已处理过渡运镜内容:', processedContent);
                                                                                 
                                                                                 cell.innerHTML = `<div style="font-size:12px;line-height:1.4;color:#374151;">${processedContent}</div>`;
                                                                                 
                                                                                 console.log('✅ HTML表格已更新为与Excel一致的内容');
                                                                             } else {
                                                                                 console.error('❌ 从Excel获取过渡运镜内容失败:', excelData);
                                                                                 // 如果无法从Excel获取，使用API返回的原始内容作为备选
                                                                                 let transitionContent = data.results[0].content.trim();
                                                                                 cell.innerHTML = `<div style="font-size:12px;line-height:1.4;color:#374151;">${transitionContent}</div>`;
                                                                             }
                                                                         })
                                                                         .catch(error => {
                                                                             console.error('❌ 从Excel获取过渡运镜内容请求失败:', error);
                                                                             // 如果无法从Excel获取，使用API返回的原始内容作为备选
                                                                             let transitionContent = data.results[0].content.trim();
                                                                             cell.innerHTML = `<div style="font-size:12px;line-height:1.4;color:#374151;">${transitionContent}</div>`;
                                                                         });
                                                                     } else {
                                                                         console.error('❌ 过渡运镜生成失败:', data);
                                                                         cell.innerHTML = '<div style="color:#ef4444;font-size:12px;">生成失败，请重试</div>';
                                                                     }
                                                                 })
                                                                 .catch(error => {
                                                                     console.error('❌ 过渡运镜生成请求失败:', error);
                                                                     cell.innerHTML = '<div style="color:#ef4444;font-size:12px;">网络错误，请重试</div>';
                                                                 })
                                                                 .finally(() => {
                                                                     // 恢复按钮状态
                                                                     btn.disabled = false;
                                                                     btn.textContent = '重新生成';
                                                                     btn.style.background = '#3b82f6';
                                                                     console.log('🔄 过渡运镜按钮状态已恢复');
                                                                 });
                                                             } else {
                                                                 console.error('❌ 开始画面提取失败:', startSceneData);
                                                                 cell.innerHTML = '<div style="color:#ef4444;font-size:12px;">开始画面提取失败，请重试</div>';
                                                                 btn.disabled = false;
                                                                 btn.textContent = '生成过渡运镜';
                                                                 btn.style.background = '#3b82f6';
                                                             }
                                                         })
                                                         .catch(error => {
                                                             console.error('❌ 开始画面提取请求失败:', error);
                                                             cell.innerHTML = '<div style="color:#ef4444;font-size:12px;">开始画面提取网络错误，请重试</div>';
                                                             btn.disabled = false;
                                                             btn.textContent = '生成过渡运镜';
                                                             btn.style.background = '#3b82f6';
                                                         });
                                                     } else {
                                                         console.error('❌ 结束画面提取失败:', endSceneData);
                                                         cell.innerHTML = '<div style="color:#ef4444;font-size:12px;">结束画面提取失败，请重试</div>';
                                                         btn.disabled = false;
                                                         btn.textContent = '生成过渡运镜';
                                                         btn.style.background = '#3b82f6';
                                                     }
                                                 })
                                                 .catch(error => {
                                                     console.error('❌ 结束画面提取请求失败:', error);
                                                     cell.innerHTML = '<div style="color:#ef4444;font-size:12px;">结束画面提取网络错误，请重试</div>';
                                                     btn.disabled = false;
                                                     btn.textContent = '生成过渡运镜';
                                                     btn.style.background = '#3b82f6';
                                                 });
                                             }
                                            
                                            // 生成汇总提示词的函数
                                            function generateSummaryPrompt(prompts) {
                                                console.log('🔄 开始生成汇总提示词...');
                                                
                                                // 构建发送给LLM的完整消息内容
                                                let llmMessage = "请分析以下生图提示词，找出每页生图提示词的标准开头、标准结尾的公共相同部分，然后按照指定格式提取和组织内容。\n\n";
                                                
                                                // 添加所有页数和生图提示词内容
                                                prompts.forEach((prompt, index) => {
                                                    llmMessage += `${prompt.page}：${prompt.prompt}\n\n`;
                                                });
                                                
                                                llmMessage += "\n处理要求：\n";
                                                llmMessage += "1. 智能分析并识别所有生图提示词中的公共标准开头和标准结尾部分\n";
                                                llmMessage += "2. 提取每页生图提示词去除公共部分后的中间故事描述完整内容（不要修改或遗漏）\n";
                                                llmMessage += "3. 按照以下格式组织内容：\n";
                                                llmMessage += "   开篇词：这是一个连环画绘本故事的" + prompts.length + "页连续镜头，请按我的需求，画出连续分镜头，每页场景可以合理优化镜头和视角，并保持角色和服装等前后高度一致性，不要输出任何文字内容。\n";
                                                llmMessage += "   前缀：你智能分析判断出的每页生图提示词的标准开头公共相同部分\n";
                                                llmMessage += "   中间内容：" + prompts.map((prompt, index) => {
                                                    return `${prompt.page}：去除标准开头和标准结尾后的中间故事描述完整内容`;
                                                }).join('。') + "\n";
                                                llmMessage += "   后缀：你智能分析判断出的每页生图提示词的标准结尾公共相同部分\n";
                                                llmMessage += "   最终格式：开篇词+前缀+：+第1页：故事描述内容。第N页：故事描述内容+后缀\n";
                                                llmMessage += "4. 返回完整的汇总提示词，格式为：开篇词+前缀+中间内容+后缀，不要包含任何其他说明文字或格式标记\n";
                                                llmMessage += "5. 注意：每页生图提示词的标准开头、标准结尾的公共相同部分需要你智能比对思考后自行判断\n";
                                                
                                                console.log('📝 发送给LLM的完整消息:', llmMessage);
                                                
                                                // 调用后端API
                                                fetch('/generate', {
                                                    method: 'POST',
                                                    headers: {
                                                        'Content-Type': 'application/json',
                                                    },
                                                    body: JSON.stringify({
                                                        prompt: llmMessage,
                                                        model: '{{ gemini_flash_model }}',
                                                        total_pages: prompts.length  // 传递当前故事的总页数
                                                    })
                                                })
                                                .then(response => response.json())
                                                .then(data => {
                                                    console.log('📨 汇总API响应:', data);
                                                    
                                                    const summaryCell = document.getElementById('summary-prompt-cell');
                                                    const summaryViewLink = document.getElementById('summary-view-link');
                                                    
                                                    if (data.status === 'success' && data.results && data.results.length > 0) {
                                                        // 提取API返回的核心故事内容
                                                        let coreStoryContent = data.results[0].content;
                                                        
                                                        // 彻底清理HTML标签和实体
                                                        if (coreStoryContent.includes('<')) {
                                                            const tempDiv = document.createElement('div');
                                                            tempDiv.innerHTML = coreStoryContent;
                                                            coreStoryContent = tempDiv.textContent || tempDiv.innerText || '';
                                                        }
                                                        
                                                        // 清理所有HTML标签、实体和特殊字符
                                                        coreStoryContent = coreStoryContent
                                                            .replace(/<[^>]*>/g, '')           // 移除HTML标签
                                                            .replace(/&[^;]+;/g, '')           // 移除HTML实体
                                                            .replace(/[\r\n\t]+/g, ' ')        // 规范化空白字符
                                                            .replace(/\s+/g, ' ')              // 合并多个空格
                                                            .trim();                           // 去除首尾空白
                                                        
                                                        // 直接使用LLM返回的完整汇总提示词内容
                                                        const finalSummary = coreStoryContent;
                                                        
                                                        // 显示纯文本内容（不使用innerHTML避免HTML解析）
                                                        summaryCell.textContent = finalSummary;
                                                        summaryViewLink.style.color = '#3b82f6';
                                                        summaryViewLink.style.pointerEvents = 'auto';
                                                        summaryViewLink.onclick = function(e) {
                                                            e.preventDefault();
                                                            showPromptModal([{prompt: finalSummary, page: '汇总', index: 0}], 0, tableDiv);
                                                        };
                                                        
                                                        console.log('✅ 汇总提示词生成成功');
                                                        console.log('📊 总页数:', prompts.length);
                                                        console.log('📝 LLM返回的完整汇总提示词:', coreStoryContent);
                                                        console.log('🎯 最终汇总提示词:', finalSummary.substring(0, 100) + '...');
                                                    } else {
                                                        summaryCell.innerHTML = '<div style="color:#ef4444;">汇总生成失败：' + (data.message || '未知错误') + '</div>';
                                                        console.error('❌ 汇总生成失败:', data);
                                                    }
                                                })
                                                .catch(error => {
                                                    console.error('❌ 汇总API调用失败:', error);
                                                    const summaryCell = document.getElementById('summary-prompt-cell');
                                                    summaryCell.innerHTML = '<div style="color:#ef4444;">汇总生成失败：网络错误</div>';
                                                });
                                            }

                                            // 动态插入"配音表生成"按钮（仅插入一次）
                                            if (!document.querySelector('.next-step-btn')) {
                                                const nextStepBtn = document.createElement('button');
                                                nextStepBtn.className = 'next-step-btn';
                                                nextStepBtn.textContent = '生成配音表';
                                                nextStepBtn.style.marginTop = '16px';
                                                nextStepBtn.style.background = '#10b981';
                                                nextStepBtn.style.color = '#fff';
                                                nextStepBtn.style.border = 'none';
                                                nextStepBtn.style.padding = '8px 16px';
                                                nextStepBtn.style.borderRadius = '6px';
                                                nextStepBtn.style.cursor = 'pointer';
                                                functionsDiv.appendChild(nextStepBtn);
                                                nextStepBtn.onclick = async function() {
                                                    nextStepBtn.disabled = true;
                                                    nextStepBtn.textContent = '正在生成配音表...';
                                                    // 取原始HTML内容
                                                    const htmlContent = richEditorDiv.innerHTML;
                                                    try {
                                                        const response = await fetch('/generate_speech_table', {
                                                            method: 'POST',
                                                            headers: { 'Content-Type': 'application/json' },
                                                            body: JSON.stringify({ html_text: htmlContent })
                                                        });
                                                        const data = await response.json();
                                                        if (data.status === 'success') {
                                                            const tableDiv2 = document.createElement('div');
                                                            tableDiv2.className = 'table-container';
                                                            tableDiv2.innerHTML = data.table_html;
                                                            functionsDiv.appendChild(tableDiv2);
                                                            nextStepBtn.textContent = '配音表已生成';
                                                            // 在生成配音表按钮生成配音表成功后，动态插入"SRT配音表生成"按钮
                                                            if (!document.querySelector('.next-srt-btn')) {
                                                                const nextSrtBtn = document.createElement('button');
                                                                nextSrtBtn.className = 'next-srt-btn';
                                                                nextSrtBtn.textContent = 'SRT配音表生成';
                                                                nextSrtBtn.style.marginTop = '16px';
                                                                nextSrtBtn.style.background = '#f59e42';
                                                                nextSrtBtn.style.color = '#fff';
                                                                nextSrtBtn.style.border = 'none';
                                                                nextSrtBtn.style.padding = '8px 16px';
                                                                nextSrtBtn.style.borderRadius = '6px';
                                                                nextSrtBtn.style.cursor = 'pointer';
                                                                functionsDiv.appendChild(nextSrtBtn);
                                                                nextSrtBtn.onclick = async function() {
                                                                    nextSrtBtn.disabled = true;
                                                                    nextSrtBtn.textContent = '正在生成SRT及配音表...';
                                                                    try {
                                                                        const srtResp = await fetch('/generate_srt_files', {
                                                                            method: 'POST',
                                                                            headers: { 'Content-Type': 'application/json' },
                                                                            body: JSON.stringify({ html_text: htmlContent })
                                                                        });
                                                                        const srtData = await srtResp.json();
                                                                        if (srtData.status === 'success') {
                                                                            const srtTableDiv = document.createElement('div');
                                                                            srtTableDiv.className = 'table-container';
                                                                            srtTableDiv.innerHTML = srtData.table_html;
                                                                            functionsDiv.appendChild(srtTableDiv);
                                                                            nextSrtBtn.textContent = '配音SRT表已生成';
                                                                            bindSrtModalEvents(srtTableDiv);
                                                                        } else {
                                                                            nextSrtBtn.textContent = '生成失败，请重试';
                                                                            alert(srtData.message || '生成SRT配音表失败');
                                                                        }
                                                                    } catch (err) {
                                                                        nextSrtBtn.textContent = '生成失败，请重试';
                                                                        alert('网络错误或服务器异常');
                                                                    }
                                                                };
                                                            }
                                                        } else {
                                                            nextStepBtn.textContent = '生成失败，请重试';
                                                            alert(data.message || '生成配音表失败');
                                                        }
                                                    } catch (err) {
                                                        nextStepBtn.textContent = '生成失败，请重试';
                                                        alert('网络错误或服务器异常');
                                                    }
                                                };
                                            }
                                        };
                                    }
                                }
                                const textContainer = document.createElement('div');
                                textContainer.className = 'text-container';

                                // 添加元数据显示
                                if (item.metadata) {
                                    // 创建元数据容器
                                    const metadataContainer = document.createElement('div');
                                    metadataContainer.className = 'message-metadata';

                                    // 添加角色标识
                                    if (item.metadata.role) {
                                        const roleSpan = document.createElement('span');
                                        roleSpan.className = `role-badge role-${item.metadata.role}`;
                                        roleSpan.textContent = item.metadata.role === 'assistant' ? 'AI' : item.metadata.role;
                                        metadataContainer.appendChild(roleSpan);
                                    }

                                    // 添加序列号
                                    if (item.metadata.sequence) {
                                        const sequenceSpan = document.createElement('span');
                                        sequenceSpan.className = 'sequence-number';
                                        sequenceSpan.textContent = `#${item.metadata.sequence}`;
                                        metadataContainer.appendChild(sequenceSpan);
                                    }

                                    // 添加时间戳
                                    if (item.metadata.timestamp) {
                                        const timeSpan = document.createElement('span');
                                        timeSpan.className = 'timestamp';
                                        timeSpan.textContent = item.metadata.timestamp;
                                        metadataContainer.appendChild(timeSpan);
                                    }

                                    // 如果是对话内容，添加对话标记
                                    if (item.metadata.is_dialogue) {
                                        textContainer.classList.add('dialogue-content');

                                        const dialogueIcon = document.createElement('span');
                                        dialogueIcon.className = 'content-type-icon dialogue-icon';
                                        dialogueIcon.title = '包含对话内容';
                                        dialogueIcon.innerHTML = '<i class="icon">💬</i>';
                                        metadataContainer.appendChild(dialogueIcon);
                                    }

                                    // 添加内容类型指示器
                                    if (item.metadata.content_type) {
                                        const contentTypeContainer = document.createElement('div');
                                        contentTypeContainer.className = 'content-type-container';

                                        if (item.metadata.content_type.has_story) {
                                            const storyIcon = document.createElement('span');
                                            storyIcon.className = 'content-type-icon story-icon';
                                            storyIcon.title = '包含故事内容';
                                            storyIcon.innerHTML = '<i class="icon">📖</i>';
                                            contentTypeContainer.appendChild(storyIcon);
                                        }

                                        if (item.metadata.content_type.has_prompt) {
                                            const promptIcon = document.createElement('span');
                                            promptIcon.className = 'content-type-icon prompt-icon';
                                            promptIcon.title = '包含生图提示词';
                                            promptIcon.innerHTML = '<i class="icon">🎨</i>';
                                            contentTypeContainer.appendChild(promptIcon);
                                        }

                                        if (contentTypeContainer.children.length > 0) {
                                            metadataContainer.appendChild(contentTypeContainer);
                                        }
                                    }

                                    // 添加情感标签
                                    if (item.metadata.emotions && item.metadata.emotions.length > 0) {
                                        const emotionsContainer = document.createElement('div');
                                        emotionsContainer.className = 'emotions-container';

                                        item.metadata.emotions.forEach(emotion => {
                                            const emotionTag = document.createElement('span');
                                            emotionTag.className = `emotion-tag emotion-${emotion}`;
                                            emotionTag.textContent = emotion;
                                            emotionsContainer.appendChild(emotionTag);
                                        });

                                        metadataContainer.appendChild(emotionsContainer);
                                    }

                                    // 添加主题标签
                                    if (item.metadata.themes && item.metadata.themes.length > 0) {
                                        const themesContainer = document.createElement('div');
                                        themesContainer.className = 'themes-container';

                                        item.metadata.themes.forEach(theme => {
                                            const themeTag = document.createElement('span');
                                            themeTag.className = 'theme-tag';
                                            themeTag.textContent = theme;
                                            themesContainer.appendChild(themeTag);
                                        });

                                        metadataContainer.appendChild(themesContainer);
                                    }

                                    // 添加统计信息按钮
                                    if (item.metadata.statistics) {
                                        const statsButton = document.createElement('button');
                                        statsButton.className = 'stats-button';
                                        statsButton.textContent = '统计';
                                        statsButton.title = '显示文本统计信息';

                                        statsButton.addEventListener('click', function() {
                                            const stats = item.metadata.statistics;
                                            alert(`文本统计信息:\n字符数: ${stats.char_count}\n词数: ${stats.word_count}\n句子数: ${stats.sentence_count}`);
                                        });

                                        metadataContainer.appendChild(statsButton);
                                    }

                                    // 添加原始文本查看按钮
                                    if (item.raw_text) {
                                        const rawTextButton = document.createElement('button');
                                        rawTextButton.className = 'raw-text-button';
                                        rawTextButton.textContent = '原文';
                                        rawTextButton.title = '查看原始未格式化文本';

                                        rawTextButton.addEventListener('click', function() {
                                            // 创建模态框显示原始文本
                                            const modal = document.createElement('div');
                                            modal.className = 'modal';

                                            const modalContent = document.createElement('div');
                                            modalContent.className = 'modal-content';

                                            const closeBtn = document.createElement('span');
                                            closeBtn.className = 'close-button';
                                            closeBtn.innerHTML = '&times;';
                                            closeBtn.onclick = function() {
                                                document.body.removeChild(modal);
                                            };

                                            const title = document.createElement('h3');
                                            title.textContent = '原始未格式化文本';

                                            const content = document.createElement('pre');
                                            content.className = 'raw-text';
                                            content.textContent = item.raw_text;

                                            modalContent.appendChild(closeBtn);
                                            modalContent.appendChild(title);
                                            modalContent.appendChild(content);
                                            modal.appendChild(modalContent);

                                            // 点击模态框外部关闭
                                            modal.onclick = function(event) {
                                                if (event.target === modal) {
                                                    document.body.removeChild(modal);
                                                }
                                            };

                                            document.body.appendChild(modal);
                                        });

                                        metadataContainer.appendChild(rawTextButton);
                                    }

                                    textContainer.appendChild(metadataContainer);
                                }
                            });
                        } else {
                            results.innerHTML = '<p>未返回任何结果。</p>';
                        }
                    } else {
                        // 处理错误状态
                        statusMessage.className = 'status error';
                        statusMessage.textContent = data.message || '生成过程中发生错误';
                        statusMessage.style.display = 'block';

                        if (data.error_details) {
                            const errorDetails = document.createElement('div');
                            errorDetails.className = 'error-details';
                            errorDetails.textContent = data.error_details;
                            results.appendChild(errorDetails);
                        }
                    }
                } catch (error) {
                    console.error('请求错误:', error);
                    statusMessage.className = 'status error';
                    statusMessage.textContent = error.message || '无法与服务器通信';
                    statusMessage.style.display = 'block';
                } finally {
                    loadingIndicator.style.display = 'none';
                    generateBtn.disabled = false;
                }
            });

            // 测试API连接
            testApiBtn.addEventListener('click', async function() {
                console.log('测试API按钮被点击');
                testApiBtn.disabled = true;
                apiInfo.textContent = '正在测试API连接...';

                try {
                    console.log('开始发送测试API请求...');
                    const response = await fetch(`/test_api?model=${encodeURIComponent('{{ gemini_flash_model }}')}`, { method: 'GET' });
                    console.log('测试API请求已发送，状态码:', response.status);
                    const data = await response.json();
                    console.log('测试API响应数据:', data);

                    if (data.status === 'success') {
                        apiInfo.textContent = `API连接成功: ${data.test_text || 'OK'}`;
                    } else {
                        apiInfo.textContent = `API测试失败: ${data.message}`;
                    }
                } catch (error) {
                    console.error('测试API请求错误:', error);
                    apiInfo.textContent = '测试API连接时出错';
                } finally {
                    testApiBtn.disabled = false;
                }
            });



            // SRT表格弹窗功能
            function bindSrtModalEvents(container) {
                const srtLinks = container.querySelectorAll('a[href^="/srt_temp/"]');
                srtLinks.forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const url = link.getAttribute('href');
                        const filename = url.split('/').pop();

                        // 打开SRT编辑模板页面
                        const width = 800;
                        const height = 600;
                        const left = (window.innerWidth - width) / 2;
                        const top = (window.innerHeight - height) / 2;

                        const newWindow = window.open(
                            `/templates/srt_edit_template.html?file=${filename}`,
                            'srt_editor',
                            `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes`
                        );

                        // 监听从编辑窗口返回的消息
                        window.addEventListener('message', function(event) {
                            if (event.data && event.data.type === 'srt_updated') {
                                // 可以在这里进行刷新或其他操作
                                console.log('SRT文件已更新:', event.data.filename);
                            }
                        });
                    });
                });
            }

            // 在表格初始化时添加日志记录
            window.tableLogger.logOperation('table_init', -1, 'all', '表格初始化');

            // 监听表格单元格编辑
            document.addEventListener('cellEdited', function(e) {
                window.tableLogger.logCellEdit(
                    e.detail.row,
                    e.detail.column,
                    e.detail.newValue,
                    e.detail.oldValue
                );
            });

            // 监听表格行删除
            document.addEventListener('rowDeleted', function(e) {
                window.tableLogger.logRowDelete(e.detail.row, e.detail.rowData);
            });

            // 监听表格行添加
            document.addEventListener('rowAdded', function(e) {
                window.tableLogger.logRowAdd(e.detail.row, e.detail.rowData);
            });

            // 监听表格排序
            document.addEventListener('tableSorted', function(e) {
                window.tableLogger.logSort(e.detail.column, e.detail.direction);
            });

            // 监听表格过滤
            document.addEventListener('tableFiltered', function(e) {
                window.tableLogger.logFilter(e.detail.column, e.detail.filter);
            });

            // 修改生成配音表格的函数
            async function generateSpeechTable() {
                try {
                    const htmlText = document.getElementById('story-content').innerHTML;
                    const response = await fetch('/generate_speech_table', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ html_text: htmlText })
                    });

                    const data = await response.json();
                    if (data.status === 'success') {
                        window.tableLogger.setLogId(data.log_id);
                        window.tableLogger.logOperation('speech_table_generated', -1, 'all', '配音表格生成成功');
                        // ... 更新表格显示 ...
                    } else {
                        window.tableLogger.logOperation('speech_table_error', -1, 'all', data.message);
                        // ... 错误处理 ...
                    }
                } catch (error) {
                    window.tableLogger.logOperation('speech_table_exception', -1, 'all', error.message);
                    // ... 错误处理 ...
                }
            }

            // 修改生成SRT文件的函数
            async function generateSrtFiles() {
                try {
                    const htmlText = document.getElementById('speech-table').innerHTML;
                    const operations = window.tableLogger.getOperations();

                    const response = await fetch('/generate_srt_files', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            html_text: htmlText,
                            table_operations: operations
                        })
                    });

                    const data = await response.json();
                    if (data.status === 'success') {
                        window.tableLogger.setLogId(data.log_id);
                        window.tableLogger.logOperation('srt_files_generated', -1, 'all', 'SRT文件生成成功');
                        // ... 更新表格显示 ...
                    } else {
                        window.tableLogger.logOperation('srt_files_error', -1, 'all', data.message);
                        // ... 错误处理 ...
                    }
                } catch (error) {
                    window.tableLogger.logOperation('srt_files_exception', -1, 'all', error.message);
                    // ... 错误处理 ...
                }
            }

            // 修改音频预览函数
            async function previewAudio(filename) {
                try {
                    window.tableLogger.logOperation('audio_preview_start', -1, 'file', filename);
                    const audio = new Audio(`/preview_voice/${filename}`);

                    audio.onplay = () => {
                        window.tableLogger.logAudioPreview(filename, true);
                    };

                    audio.onerror = (e) => {
                        window.tableLogger.logAudioPreview(filename, false, e.message);
                    };

                    await audio.play();
                } catch (error) {
                    window.tableLogger.logAudioPreview(filename, false, error.message);
                    // ... 错误处理 ...
                }
            }

            // 修改SRT文件下载函数
            async function downloadSrtFile(filename) {
                try {
                    window.tableLogger.logOperation('srt_download_start', -1, 'file', filename);
                    const response = await fetch(`/srt_temp/${filename}`);

                    if (response.ok) {
                        window.tableLogger.logOperation('srt_download_success', -1, 'file', filename);
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                    } else {
                        window.tableLogger.logOperation('srt_download_error', -1, 'file', filename);
                        // ... 错误处理 ...
                    }
                } catch (error) {
                    window.tableLogger.logOperation('srt_download_exception', -1, 'file', filename);
                    // ... 错误处理 ...
                }
            }

            function showPromptModal(prompts, idx, tableDiv) {
                const modal = document.createElement('div');
                modal.style.position = 'fixed';
                modal.style.left = 0;
                modal.style.top = 0;
                modal.style.width = '100vw';
                modal.style.height = '100vh';
                modal.style.background = 'rgba(0,0,0,0.3)';
                modal.style.display = 'flex';
                modal.style.alignItems = 'center';
                modal.style.justifyContent = 'center';
                modal.style.zIndex = 9999;
                const win = document.createElement('div');
                win.style.background = '#fff';
                win.style.borderRadius = '8px';
                win.style.padding = '24px';
                win.style.minWidth = '400px';
                win.style.boxShadow = '0 2px 16px rgba(0,0,0,0.15)';
                
                // 判断是否为汇总提示词
                const isSummary = prompts[idx].page === '汇总';
                const title = isSummary ? '编辑汇总提示词' : '编辑生图提示词';
                
                win.innerHTML = `
                    <div style='display:flex;justify-content:space-between;align-items:center;'>
                        <span style='font-size:18px;font-weight:bold;'>${title}</span>
                        <button id='closePromptModal' style='font-size:20px;background:none;border:none;cursor:pointer;'>&times;</button>
                    </div>
                    <div style='margin:16px 0;'>
                        <textarea id='promptEditBox' style='width:100%;height:120px;padding:8px;border:1px solid #ccc;border-radius:4px;'>${prompts[idx].prompt}</textarea>
                    </div>
                    <div style='text-align:right;'>
                        <button id='confirmPromptEdit' style='background:#2563eb;color:#fff;padding:6px 18px;border:none;border-radius:4px;cursor:pointer;'>确定返回</button>
                    </div>
                `;
                modal.appendChild(win);
                document.body.appendChild(modal);
                win.querySelector('#closePromptModal').onclick = () => document.body.removeChild(modal);
                win.querySelector('#confirmPromptEdit').onclick = () => {
                    const newVal = win.querySelector('#promptEditBox').value.trim();
                    prompts[idx].prompt = newVal;
                    
                    // 根据是否为汇总提示词来更新不同的表格单元格
                    if (isSummary) {
                        // 更新汇总提示词单元格
                        const summaryCell = document.getElementById('summary-prompt-cell');
                        if (summaryCell) {
                            summaryCell.textContent = newVal;
                        }
                    } else {
                        // 更新普通提示词表格行
                        const rows = tableDiv.querySelectorAll('tbody tr');
                        if (rows[idx]) {
                            rows[idx].children[2].textContent = newVal;
                        }
                    }
                    
                    document.body.removeChild(modal);
                };
            }
        });

// 项目保存功能已移除
// 此处原有项目保存相关的JavaScript代码已被移除
                
// 项目保存和加载功能已移除
// 此处原有项目保存和加载相关的JavaScript代码已被移除
            
// 项目加载功能已移除
// 此处原有获取项目列表和创建项目选择对话框的JavaScript代码已被移除
// 项目加载、保存和管理功能已移除
// 此处原有项目加载、保存和管理相关的JavaScript代码已被移除
    </script>
</body>
</html>