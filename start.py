import base64
import os
import random
import time
import logging
import sys
from datetime import datetime
import uuid
import re
import threading
from flask import Flask, render_template, request, jsonify, send_from_directory, send_file
#import google.generativeai as genai
from google import genai
import config
from config import API_KEYS, GEMINI_FLASH_MODEL, GEMINI_IMAGE_MODEL, build_camera_prompt
import io
from generate_speech_table import generate_speech_table, generate_srt_files_and_table

from bs4 import BeautifulSoup
try:
    from openpyxl import load_workbook
except ImportError:
    load_workbook = None  # Will error if not installed

# 数据保存功能已移除
data_saver = None

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] [%(name)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

# Create specific loggers for different components
speech_logger = logging.getLogger('speech_table')
srt_logger = logging.getLogger('srt_files')
api_logger = logging.getLogger('api_operations')

# Keep track of used API keys for cycling
current_key_index = 0
INVALID_API_KEYS = set()
# API密钥检查时间戳（每小时检查一次）
last_api_check_time = 0
API_CHECK_INTERVAL = 3600  # 1小时 = 3600秒

# Create Flask app
app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB file size limit
app.config['UPLOAD_FOLDER'] = 'temp'

# Ensure temp directory exists
temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
if not os.path.exists(temp_dir):
    os.makedirs(temp_dir)
    logging.info(f"Created temp directory: {temp_dir}")

def get_next_api_key():
    """Get the next API key using cycling strategy"""
    global current_key_index
    valid_keys = [key for key in API_KEYS if key not in INVALID_API_KEYS]
    print(f"[步骤] 当前有效API KEY数量: {len(valid_keys)}，已失效KEY数量: {len(INVALID_API_KEYS)}")
    if not valid_keys:
        logging.error("No valid API keys available")
        print("[错误] 没有可用的API KEY！")
        raise ValueError("No valid API keys available")
    # Select initial key randomly if it's the first use
    if current_key_index == 0:
        current_key_index = random.randint(0, len(valid_keys) - 1)
        api_logger.info(f"首次使用，随机选择API KEY索引: {current_key_index}")
        print(f"[步骤] 首次使用，随机选择KEY索引: {current_key_index}")
    else:
        # Move to next key
        current_key_index = (current_key_index + 1) % len(valid_keys)
        api_logger.info(f"轮换到下一个API KEY索引: {current_key_index}")
        print(f"[步骤] 轮换到下一个KEY索引: {current_key_index}")
    selected_key = valid_keys[current_key_index]
    api_logger.info(f"当前使用API KEY前10位: {selected_key[:10]}")
    print(f"[信息] 当前使用API KEY前10位: {selected_key[:10]}")
    return selected_key

def save_binary_file(file_name, data):
    """Save binary data to a file"""
    print(f"[步骤] 保存二进制文件: {file_name}")
    try:
        with open(file_name, "wb") as f:
            f.write(data)
        print(f"[成功] 文件保存成功: {file_name}")
        return True
    except Exception as e:
        logging.error(f"Error saving file {file_name}: {e}")
        print(f"[错误] 文件保存失败: {file_name}, 错误: {e}")
        return False

def beautify_text(text):
    """美化文本，去除多余换行，对章节进行合理分段，保持文本连贯性

    根据用户需求优化文本排版，确保特殊标记（如标题、场景描述等）保持在同一行，
    减少不必要的换行，使文本更加连贯易读。同时保留HTML标签。

    Args:
        text (str): 需要美化的原始文本

    Returns:
        str: 美化后的文本
    """
    if not text or not isinstance(text, str):
        return text

    # 保存HTML标签
    html_tags = []
    def save_html_tag(match):
        html_tags.append(match.group(0))
        return f"__HTML_TAG_{len(html_tags)-1}__"

    # 临时保存HTML标签
    text_with_placeholders = re.sub(r'<[^>]+>', save_html_tag, text)

    # 去除多余换行，保留段落分隔
    lines = text_with_placeholders.split('\n')
    processed_lines = []
    current_paragraph = []

    # 识别章节标记和特殊标题
    section_markers = [
        '【生图提示词】', '【旁白', '【旁白配音】', '【',
        '生图提示词：', '旁白：', '旁白配音：',
        '主题：', '故事：', '对白：', '场景配图生图prompt:',
        '第一页', '第二页', '第三页', '第四页', '第五页'
    ]

    # 标题和内容标记，这些标记后的内容应该与标记保持在同一行
    title_markers = ['主题：', '故事：', '对白：', '场景配图生图prompt:']

    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            # 空行作为段落分隔符
            if current_paragraph:
                processed_lines.append(' '.join(current_paragraph))
                current_paragraph = []
        elif any(marker in line for marker in section_markers):
            # 章节标记作为新段落的开始
            if current_paragraph:
                processed_lines.append(' '.join(current_paragraph))

            # 检查是否是标题标记，如果是，尝试将下一行内容合并到当前行
            if any(marker in line for marker in title_markers) and i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if next_line and not any(marker in next_line for marker in section_markers):
                    # 将标题和内容合并为一行
                    current_paragraph = [line + ' ' + next_line]
                    # 跳过下一行，因为已经处理过了
                    lines[i + 1] = ''
                else:
                    current_paragraph = [line]
            else:
                current_paragraph = [line]
        else:
            # 如果当前行不是空行且不包含章节标记，则添加到当前段落
            if current_paragraph and not any(marker in current_paragraph[0] for marker in title_markers):
                current_paragraph.append(line)
            else:
                # 如果当前没有段落或当前段落是标题，则创建新段落
                if current_paragraph:
                    processed_lines.append(' '.join(current_paragraph))
                current_paragraph = [line]

    # 添加最后一个段落
    if current_paragraph:
        processed_lines.append(' '.join(current_paragraph))

    # 合并处理后的段落，使用HTML段落标签
    result = '<p>' + '</p><p>'.join(processed_lines) + '</p>'

    # 恢复HTML标签
    for i, tag in enumerate(html_tags):
        result = result.replace(f"__HTML_TAG_{i}__", tag)

    return result

def clean_llm_html(raw_text: str) -> str:
    """
    清理LLM返回的HTML内容，去除```html、```等包裹，保持原始结构完整性。
    优化策略：最小化处理，保留LLM原始格式化效果。
    """
    if not raw_text:
        return ""

    # 去除代码块包裹标记
    cleaned = raw_text.strip()
    if cleaned.startswith('```html'):
        cleaned = cleaned[len('```html'):].strip()
    elif cleaned.startswith('```'):
        cleaned = cleaned[len('```'):].strip()
    if cleaned.endswith('```'):
        cleaned = cleaned[:-3].strip()

    # 轻量级HTML验证和修复，保持原始结构
    try:
        soup = BeautifulSoup(cleaned, "html.parser")

        # 只在必要时添加基本结构，不改变原有内容
        if not soup.find('html') and not soup.find('body'):
            # 如果既没有html也没有body标签，直接返回内容
            # 让前端处理显示，避免过度包装
            return cleaned

        return str(soup)
    except Exception as e:
        # 解析失败时返回原始内容，确保不丢失信息
        api_logger.warning(f"HTML解析失败，返回原始内容: {str(e)}")
        return cleaned

def apply_tailwind_styles(html: str) -> str:
    """
    为 LLM 返回的 HTML 添加轻量化的 Tailwind CSS 样式
    优化策略：保持原始结构，仅添加必要的美化样式
    """
    try:
        soup = BeautifulSoup(html, "html.parser")

        # 轻量化样式应用，不改变原有结构
        # 标题美化
        for h in soup.find_all(['h1', 'h2', 'h3']):
            existing_class = h.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            h['class'] = existing_class + ["font-bold", "text-2xl", "text-pink-600", "mb-4"]

        # 段落美化
        for p in soup.find_all('p'):
            existing_class = p.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            p['class'] = existing_class + ["text-lg", "leading-relaxed", "text-gray-800", "mb-2"]

        # 列表美化
        for ul in soup.find_all('ul'):
            existing_class = ul.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            ul['class'] = existing_class + ["list-disc", "pl-6", "text-base", "text-blue-700"]

        for li in soup.find_all('li'):
            existing_class = li.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            li['class'] = existing_class + ["mb-1"]

        # 表格美化
        for table in soup.find_all('table'):
            existing_class = table.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            table['class'] = existing_class + ["min-w-full", "bg-white", "border", "border-gray-300", "rounded-lg", "shadow"]

        for th in soup.find_all('th'):
            existing_class = th.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            th['class'] = existing_class + ["bg-blue-200", "text-blue-900", "px-4", "py-2"]

        for td in soup.find_all('td'):
            existing_class = td.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            td['class'] = existing_class + ["border", "px-4", "py-2"]

        return str(soup)
    except Exception as e:
        api_logger.warning(f"Tailwind样式应用失败，返回原始HTML: {str(e)}")
        return html

def check_llm_interface(api_key: str, model: str = GEMINI_FLASH_MODEL) -> dict:
    """
    检测LLM接口是否正常工作
    
    Args:
        api_key: API密钥
        model: 模型名称
    
    Returns:
        dict: 包含检测结果的字典
    """
    try:
        import socket
        import time
        import signal
        
        # 快速网络连接检测
        def quick_network_check():
            try:
                # 尝试连接到 Google DNS，超时时间很短
                socket.create_connection(("8.8.8.8", 53), timeout=3)
                return True
            except:
                return False
        
        print("[检测] 正在检测网络连接...")
        if not quick_network_check():
            print("[警告] 网络连接不可用")
            return {"status": "warning", "message": "网络连接不可用"}
        
        print("[检测] 网络连接正常，正在检测LLM接口状态...")
        api_logger.info("检测LLM接口状态")
        
        # 设置信号处理器来处理超时
        def timeout_handler(signum, frame):
            raise TimeoutError("API检测超时")
        
        # 在Windows上使用线程超时而不是信号
        import threading
        result = {"status": "error", "message": "未知错误"}
        exception_occurred = None
        
        def api_check_thread():
            nonlocal result, exception_occurred
            try:
                client = genai.Client(api_key=api_key)
                init_prompt = "请开始一个新的话题，忘掉之前的资料。你明白就回复OK"
                
                init_response = client.models.generate_content(
                    model=model,
                    contents=init_prompt
                )
                init_text = getattr(init_response, "text", "")
                
                if "OK" in init_text.upper() or "好" in init_text or "明白" in init_text:
                    result = {"status": "success", "message": "接口正常"}
                else:
                    result = {"status": "error", "message": f"接口响应异常: {init_text}"}
            except Exception as e:
                exception_occurred = e
        
        # 启动检测线程
        thread = threading.Thread(target=api_check_thread)
        thread.daemon = True
        thread.start()
        thread.join(timeout=10)  # 10秒超时
        
        if thread.is_alive():
            print("[警告] API检测超时")
            return {"status": "warning", "message": "API检测超时"}
        
        if exception_occurred:
            raise exception_occurred
        
        if result["status"] == "success":
            print("[检测] LLM接口检测成功")
            api_logger.info("LLM接口检测成功")
        else:
            print(f"[检测] LLM接口响应异常: {result['message']}")
            api_logger.warning(f"LLM接口响应异常: {result['message']}")
        
        return result
            
    except (socket.timeout, TimeoutError, ConnectionError) as e:
        error_msg = f"网络连接超时或失败: {str(e)}"
        print(f"[警告] {error_msg}")
        print("[提示] 网络连接问题，程序将继续运行，但LLM功能可能受限")
        api_logger.warning(error_msg)
        return {"status": "warning", "message": "网络连接问题，程序继续运行"}
    except KeyboardInterrupt:
        error_msg = "用户中断操作"
        print(f"[信息] {error_msg}")
        api_logger.info(error_msg)
        return {"status": "interrupted", "message": "用户中断"}
    except Exception as e:
        error_msg = f"LLM接口检测失败: {str(e)}"
        print(f"[检测] {error_msg}")
        print("[提示] LLM接口检测失败，程序将继续运行，但LLM功能可能受限")
        api_logger.error(error_msg)
        return {"status": "error", "message": error_msg}

def periodic_api_key_check():
    """
    定期检查API密钥有效性的后台任务
    每小时检查一次所有API密钥，清理失效的密钥
    """
    global last_api_check_time, INVALID_API_KEYS
    
    while True:
        try:
            current_time = time.time()
            # 检查是否需要进行API密钥检查
            if current_time - last_api_check_time >= API_CHECK_INTERVAL:
                print("[定时检查] 开始检查API密钥有效性...")
                api_logger.info("开始定时检查API密钥有效性")
                
                # 重置无效密钥集合，重新验证所有密钥
                old_invalid_count = len(INVALID_API_KEYS)
                INVALID_API_KEYS.clear()
                
                valid_count = 0
                total_count = len(API_KEYS)
                
                for i, api_key in enumerate(API_KEYS):
                    print(f"[定时检查] 检查密钥 {i+1}/{total_count}...")
                    result = check_llm_interface(api_key)
                    
                    # 将success和warning状态都视为有效密钥
                    # warning通常表示网络问题，但密钥本身可能是有效的
                    if result["status"] in ["success", "warning"]:
                        valid_count += 1
                        if result["status"] == "success":
                            print(f"[定时检查] 密钥 {i+1} 有效")
                        else:
                            print(f"[定时检查] 密钥 {i+1} 网络问题但保持有效: {result['message']}")
                    else:
                        INVALID_API_KEYS.add(api_key)
                        print(f"[定时检查] 密钥 {i+1} 无效: {result['message']}")
                        api_logger.warning(f"定时检查发现无效密钥: {result['message']}")
                    
                    # 避免请求过于频繁
                    time.sleep(2)
                
                # 更新检查时间戳
                last_api_check_time = current_time
                
                print(f"[定时检查] 完成，有效密钥: {valid_count}/{total_count}，无效密钥: {len(INVALID_API_KEYS)}")
                api_logger.info(f"定时检查完成，有效密钥: {valid_count}/{total_count}，无效密钥: {len(INVALID_API_KEYS)}")
                
                if valid_count == 0:
                    print("[定时检查] 警告：所有API密钥都无效！")
                    api_logger.error("定时检查发现所有API密钥都无效")
            
            # 每10分钟检查一次是否需要进行密钥验证
            time.sleep(600)  # 10分钟
            
        except Exception as e:
            error_msg = f"定时检查API密钥时发生错误: {str(e)}"
            print(f"[定时检查] {error_msg}")
            api_logger.error(error_msg)
            time.sleep(600)  # 出错后等待10分钟再继续

def generate_with_key(
    prompt: str,
    api_key: str,
    model: str = GEMINI_FLASH_MODEL,
    max_retries: int = 3,
    is_summary: bool = False
) -> dict:
    """
    使用指定API key生成内容，支持重试机制
    
    Args:
        prompt: 用户提示词
        api_key: API密钥
        model: 模型名称
        max_retries: 最大重试次数
        is_summary: 是否为汇总提示词生成
    
    Returns:
        dict: 生成结果
    """
    # 如果是汇总提示词生成，先检测接口状态
    if is_summary:
        interface_check = check_llm_interface(api_key, model)
        if interface_check["status"] != "success":
            return {
                "status": "error",
                "message": f"接口检测失败: {interface_check['message']}"
            }
    
    retry_count = 0
    error_msg = "未知错误"
    import traceback
    while retry_count < max_retries:
        try:
            api_logger.info(f"开始第{retry_count+1}次生成尝试")
            print(f"[步骤] 第{retry_count+1}次尝试，使用API KEY前10位: {api_key[:10]}")
            client = genai.Client(api_key=api_key)
            
            # 如果不是汇总生成，仍然发送初始化提示词
            if not is_summary:
                init_prompt = "请开始一个新的话题，忘掉之前的资料。你明白就回复OK"
                print("[步骤] 发送初始化提示词，清空上下文...")
                api_logger.info("发送初始化提示词，清空上下文...")
                init_response = client.models.generate_content(
                    model=model,
                    contents=init_prompt
                )
                init_text = getattr(init_response, "text", "")
                api_logger.debug(f"初始化回复: {init_text}")
                print(f"[DEBUG] 初始化回复: {init_text}")
            # 根据是否为汇总生成选择不同的前缀
            if is_summary:
                # 汇总提示词生成不需要HTML格式化
                merged_prompt = prompt
            else:
                # 普通内容生成使用HTML格式化前缀
                prefix = "请根据中文正规出版排版规范，按页面分段、再按生图提示词分块，最简合理美化分段，并用HTML格式完善美化后输出以下完整内容："
                merged_prompt = prefix + "\n" + prompt

            api_logger.info(f"使用模型: {model}")
            api_logger.debug(f"发送Prompt (部分): {merged_prompt[:500]}...") # 避免日志过长
            print("[步骤] 开始调用Gemini模型API...")
            response = client.models.generate_content(
                model=model,
                contents=merged_prompt
            )

            # 打印原始的Google AI响应到控制台
            print(f"[DEBUG] Raw Google AI Response: {response}")

            api_logger.info("Gemini模型API调用完成")
            print("[成功] Gemini模型API调用完成，开始处理返回内容...")

            full_text = getattr(response, "text", "")
            api_logger.debug(f"变量 full_text 类型: {type(full_text)}")
            api_logger.debug(f"变量 full_text 内容: '{full_text}'") # 打印实际内容，即使是空的也容易看出来

            api_logger.debug(f"LLM返回原始文本 (部分): {full_text[:500]}...") # 避免日志过长
            api_logger.debug(f"LLM返回原始文本长度: {len(full_text)} 字符")

            results = []
            if full_text:
                if is_summary:
                    # 汇总提示词生成返回纯文本
                    api_logger.debug(f"汇总提示词原始内容 (部分): {full_text[:500]}...")
                    api_logger.debug(f"汇总提示词原始内容长度: {len(full_text)} 字符")
                    results.append({
                        "type": "text",
                        "content": full_text.strip(),
                        "display_content_once": True
                    })
                else:
                    # 普通内容生成使用HTML格式化
                    formatted_html = clean_llm_html(full_text)
                    # 自动美化并加tailwind
                    tailwind_html = apply_tailwind_styles(formatted_html)
                    api_logger.debug(f"处理后HTML内容 (部分): {tailwind_html[:500]}...")
                    api_logger.debug(f"处理后HTML内容长度: {len(tailwind_html)} 字符")
                    results.append({
                        "type": "text",
                        "content": tailwind_html,
                        "display_content_once": True
                    })
            else:
                 api_logger.warning("LLM返回原始文本为空")

            # TODO: 如有图片内容，按需处理response结构

            api_logger.info("内容处理完成，返回结果")
            print("[步骤] 内容处理完成，返回结果")
            return {
                "status": "success",
                "results": results,
                "api_key": f"{api_key[:10]}..."
            }
        except Exception as e:
            retry_count += 1
            error_msg = str(e)
            tb_str = traceback.format_exc()
            logging.error(f"Gemini API调用异常: {error_msg}\nTraceback:\n{tb_str}")
            print(f"[错误] Gemini API调用异常: {error_msg}，重试次数: {retry_count}")
            if "API key not valid" in error_msg:
                INVALID_API_KEYS.add(api_key)
                api_logger.error(f"API KEY标记为无效: {api_key[:10]}")
                break # API key invalid, no point in retrying with the same key
            elif retry_count < max_retries:
                api_logger.warning(f"API调用失败，进行第{retry_count+1}次重试...")
                time.sleep(1)
            else:
                api_logger.error(f"API调用失败，已达最大重试次数 {max_retries}")

    api_logger.error(f"所有尝试失败，共重试{retry_count}次，错误信息: {error_msg}")
    print(f"[失败] 所有尝试失败，共重试{retry_count}次，错误信息: {error_msg}")
    return {
        "status": "error",
        "message": f"Failed after {retry_count} attempts: {error_msg}",
        "error_details": tb_str if 'tb_str' in locals() else '',
        "api_key": f"{api_key[:10]}..."
    }

@app.route('/')
def index():
    """Render main page"""
    api_key = get_next_api_key()
    # 从config.py导入提示词模版
    default_prompt = config.DEFAULT_PROMPT_TEMPLATE
    logging.info("Rendering index page")
    return render_template('index.html', api_key=f"{api_key[:10]}...", default_prompt=default_prompt, gemini_flash_model=GEMINI_FLASH_MODEL, gemini_image_model=GEMINI_IMAGE_MODEL)

def write_camera_prompt_to_sheet2(camera_prompt: str, page_id: int, project_id: str = None) -> dict:
    """
    将运镜提示词写入Excel文件的Sheet2表格
    
    Args:
        camera_prompt: 运镜提示词内容
        page_id: 当前页码（1-N）
        project_id: 项目ID，如果为None则从Sheet1获取最新的Project_ID
    
    Returns:
        dict: 写入结果
    """
    try:
        print(f"[Sheet2写入] 开始写入运镜提示词...")
        api_logger.info(f"开始写入运镜提示词到Sheet2")
        
        # 记录输入参数
        print(f"[PromptYunjing参数] 页码ID: {page_id}, 项目ID: {project_id}, 提示词长度: {len(camera_prompt)}")
        api_logger.info(f"PromptYunjing写入参数 - 页码ID: {page_id}, 项目ID: {project_id}, 提示词长度: {len(camera_prompt)}")
        
        if load_workbook is None:
            error_msg = "openpyxl库未安装"
            print(f"[PromptYunjing错误] {error_msg}")
            api_logger.error(f"PromptYunjing写入失败: {error_msg}")
            return {"status": "error", "message": error_msg}
        
        excel_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "create_image_prompt.xlsx")
        print(f"[PromptYunjing文件] Excel路径: {excel_path}")
        api_logger.info(f"PromptYunjing操作Excel文件: {excel_path}")
        
        if not os.path.exists(excel_path):
            error_msg = f"Excel文件不存在: {excel_path}"
            print(f"[PromptYunjing错误] {error_msg}")
            api_logger.error(f"PromptYunjing写入失败: {error_msg}")
            return {"status": "error", "message": error_msg}
        
        # 加载工作簿
        print(f"[PromptYunjing操作] 正在加载Excel工作簿...")
        api_logger.info(f"正在加载Excel工作簿")
        workbook = load_workbook(excel_path)
        
        # 获取或创建PromptYunjing
        if 'PromptYunjing' not in workbook.sheetnames:
            print(f"[PromptYunjing操作] PromptYunjing不存在，正在创建...")
            api_logger.info(f"PromptYunjing不存在，正在创建新的PromptYunjing")
            sheet2 = workbook.create_sheet('PromptYunjing')
            # 初始化表头
            sheet2['A1'] = 'Number'
            sheet2['B1'] = 'Project_ID'
            sheet2['C1'] = 'Page_ID'
            sheet2['D1'] = 'Page_prompt'
            print(f"[PromptYunjing操作] PromptYunjing创建完成，表头已初始化")
            api_logger.info(f"PromptYunjing创建完成，表头已初始化")
        else:
            print(f"[PromptYunjing操作] PromptYunjing已存在，正在使用现有表格")
            api_logger.info(f"PromptYunjing已存在，正在使用现有表格")
            sheet2 = workbook['PromptYunjing']
        
        # 如果没有提供project_id，从StoryList获取最新的Project_ID（确保与其他表格一致）
        if project_id is None:
            print(f"[PromptYunjing查找] 未提供项目ID，正在从StoryList获取...")
            api_logger.info(f"未提供项目ID，正在从StoryList获取最新Project_ID")
            
            # 优先从StoryList获取最新的Project_ID
            if 'StoryList' in workbook.sheetnames:
                storylist_sheet = workbook['StoryList']
                # 智能查找StoryList的最后一个非空行
                last_storylist_row = 1
                for row in range(1, storylist_sheet.max_row + 1):
                    if storylist_sheet[f'A{row}'].value is not None:
                        last_storylist_row = row
                
                if last_storylist_row > 1:  # 确保有数据行
                    project_id = storylist_sheet[f'B{last_storylist_row}'].value
                    print(f"[PromptYunjing查找] 从StoryList获取到项目ID: {project_id}，行号: {last_storylist_row}")
                    api_logger.info(f"从StoryList获取到项目ID: {project_id}，行号: {last_storylist_row}")
                else:
                    error_msg = "StoryList中没有找到Project_ID"
                    print(f"[PromptYunjing错误] {error_msg}")
                    api_logger.error(f"PromptYunjing写入失败: {error_msg}")
                    return {"status": "error", "message": error_msg}
            else:
                error_msg = "StoryList不存在，无法获取Project_ID，请先生成故事内容"
                print(f"[PromptYunjing错误] {error_msg}")
                api_logger.error(f"PromptYunjing写入失败: {error_msg}")
                return {"status": "error", "message": error_msg}
        
        # 查找是否已存在相同Project_ID和Page_ID的行
        print(f"[PromptYunjing查找] 正在查找现有记录，项目ID: {project_id}, 页码ID: {page_id}")
        api_logger.info(f"正在查找现有记录，项目ID: {project_id}, 页码ID: {page_id}")
        target_row = None
        for row in range(2, sheet2.max_row + 1):
            if (sheet2[f'B{row}'].value == project_id and 
                sheet2[f'C{row}'].value == page_id):
                target_row = row
                print(f"[PromptYunjing查找] 找到现有记录，行号: {target_row}")
                api_logger.info(f"找到现有记录，行号: {target_row}")
                break
        
        # 如果没有找到，创建新行
        if target_row is None:
            print(f"[PromptYunjing创建] 未找到现有记录，正在创建新行...")
            api_logger.info(f"未找到现有记录，正在创建新行")
            # 找到PromptYunjing的最后一行
            last_row = 1
            for row in range(1, sheet2.max_row + 1):
                if sheet2[f'A{row}'].value is not None:
                    last_row = row
            
            target_row = last_row + 1
            print(f"[PromptYunjing创建] 新行号: {target_row}，上一行: {last_row}")
            api_logger.info(f"新行号: {target_row}，上一行: {last_row}")
            
            # 设置Number（自增长）
            if last_row == 1 and sheet2['A1'].value == 'Number':
                new_number = 1
            else:
                last_number = sheet2[f'A{last_row}'].value
                new_number = (last_number + 1) if isinstance(last_number, int) else 1
            
            print(f"[PromptYunjing创建] 设置编号: {new_number}")
            api_logger.info(f"设置编号: {new_number}")
            
            sheet2[f'A{target_row}'] = new_number
            sheet2[f'B{target_row}'] = project_id
            sheet2[f'C{target_row}'] = page_id
        
        # 写入运镜提示词
        print(f"[PromptYunjing写入] 正在写入运镜提示词到行 {target_row}...")
        api_logger.info(f"正在写入运镜提示词到行 {target_row}")
        sheet2[f'D{target_row}'] = camera_prompt
        
        # 保存文件
        print(f"[PromptYunjing保存] 正在保存Excel文件...")
        api_logger.info(f"正在保存Excel文件")
        workbook.save(excel_path)
        workbook.close()
        print(f"[PromptYunjing保存] Excel文件保存完成")
        api_logger.info(f"Excel文件保存完成")
        
        success_msg = f"运镜提示词已写入PromptYunjing，行号: {target_row}, Project_ID: {project_id}, Page_ID: {page_id}"
        print(f"[PromptYunjing成功] {success_msg}")
        api_logger.info(success_msg)
        
        return {"status": "success", "message": f"已写入PromptYunjing第{target_row}行，Project_ID: {project_id}, Page_ID: {page_id}"}
        
    except Exception as e:
        error_msg = f"PromptYunjing写入失败: {str(e)}"
        print(f"[PromptYunjing错误] {error_msg}")
        print(f"[PromptYunjing错误] 异常类型: {type(e).__name__}")
        print(f"[PromptYunjing错误] 异常详情: {str(e)}")
        api_logger.error(f"PromptYunjing写入异常 - 类型: {type(e).__name__}, 详情: {str(e)}")
        api_logger.error(error_msg)
        return {"status": "error", "message": error_msg}

def initialize_sheet3_structure(project_id: str, total_pages: int) -> dict:
    """
    智能初始化PromptGuoduYunjing的内容结构，智能化管理表格数据
    
    Args:
        project_id: 项目ID
        total_pages: 总页数
    
    Returns:
        dict: 初始化结果
    """
    try:
        if load_workbook is None:
            return {"status": "error", "message": "openpyxl库未安装"}
        
        excel_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "create_image_prompt.xlsx")
        
        if not os.path.exists(excel_path):
            return {"status": "error", "message": f"Excel文件不存在: {excel_path}"}
        
        # 加载工作簿
        print(f"[PromptGuoduYunjing初始化] 正在加载Excel工作簿...")
        api_logger.info(f"正在加载Excel工作簿进行PromptGuoduYunjing初始化")
        workbook = load_workbook(excel_path)
        
        # 获取或创建PromptGuoduYunjing
        if 'PromptGuoduYunjing' not in workbook.sheetnames:
            print(f"[PromptGuoduYunjing初始化] PromptGuoduYunjing不存在，正在创建...")
            api_logger.info(f"PromptGuoduYunjing不存在，正在创建新的PromptGuoduYunjing")
            sheet3 = workbook.create_sheet('PromptGuoduYunjing')
            # 初始化表头
            sheet3['A1'] = 'Number'
            sheet3['B1'] = 'Project_ID'
            sheet3['C1'] = 'Page_ID'
            sheet3['D1'] = 'Page_Guodu_Prompt'
            print(f"[PromptGuoduYunjing初始化] PromptGuoduYunjing创建完成，表头已初始化")
            api_logger.info(f"PromptGuoduYunjing创建完成，表头已初始化")
        else:
            print(f"[PromptGuoduYunjing初始化] PromptGuoduYunjing已存在，正在使用现有表格")
            api_logger.info(f"PromptGuoduYunjing已存在，正在使用现有表格")
            sheet3 = workbook['PromptGuoduYunjing']
        
        # 检查是否已经存在该Project_ID的记录，实现智能化管理
        existing_pages = set()
        project_exists = False
        for row in range(2, sheet3.max_row + 1):
            if sheet3[f'B{row}'].value == project_id:
                project_exists = True
                page_id = sheet3[f'C{row}'].value
                if isinstance(page_id, int):
                    existing_pages.add(page_id)
        
        # 如果Project_ID已存在且页码完整，则跳过初始化
        expected_pages = set(range(1, total_pages))
        if project_exists and existing_pages >= expected_pages:
            print(f"[PromptGuoduYunjing初始化] Project_ID {project_id} 已完整存在，跳过重复初始化")
            api_logger.info(f"Project_ID {project_id} 已完整存在，跳过重复初始化")
            workbook.close()
            return {"status": "success", "message": "PromptGuoduYunjing结构已存在，无需重复初始化"}
        
        print(f"[PromptGuoduYunjing初始化] 已存在的页码: {sorted(existing_pages)}")
        api_logger.info(f"已存在的页码: {sorted(existing_pages)}")
        
        # 智能查找最后一个非空行
        last_row = 1
        for row in range(1, sheet3.max_row + 1):
            if sheet3[f'A{row}'].value is not None:
                last_row = row
        
        print(f"[PromptGuoduYunjing初始化] 当前最后一行: {last_row}")
        api_logger.info(f"当前最后一行: {last_row}")
        
        # 为缺失的页码创建行（从1到total_pages-1，因为过渡运镜是页面之间的过渡）
        rows_added = 0
        for page_id in range(1, total_pages):
            if page_id not in existing_pages:
                new_row = last_row + 1 + rows_added
                
                # 设置Number（自增长）
                if last_row == 1 and sheet3['A1'].value == 'Number':
                    new_number = 1 + rows_added
                else:
                    last_number = sheet3[f'A{last_row}'].value if rows_added == 0 else sheet3[f'A{new_row-1}'].value
                    new_number = (last_number + 1) if isinstance(last_number, int) else (1 + rows_added)
                
                sheet3[f'A{new_row}'] = new_number
                sheet3[f'B{new_row}'] = project_id
                sheet3[f'C{new_row}'] = page_id
                sheet3[f'D{new_row}'] = ''  # 过渡运镜提示词暂时为空
                
                print(f"[PromptGuoduYunjing初始化] 添加行 {new_row}: Number={new_number}, Project_ID={project_id}, Page_ID={page_id}")
                api_logger.info(f"添加行 {new_row}: Number={new_number}, Project_ID={project_id}, Page_ID={page_id}")
                
                rows_added += 1
        
        # 保存文件
        workbook.save(excel_path)
        workbook.close()
        
        if rows_added > 0:
            print(f"[Excel] PromptGuoduYunjing结构初始化完成，为Project_ID: {project_id} 添加了 {rows_added} 行")
            api_logger.info(f"PromptGuoduYunjing结构初始化完成，为Project_ID: {project_id} 添加了 {rows_added} 行")
            return {"status": "success", "message": f"PromptGuoduYunjing结构初始化完成，添加了 {rows_added} 行"}
        else:
            print(f"[Excel] PromptGuoduYunjing结构已存在，无需初始化")
            api_logger.info(f"PromptGuoduYunjing结构已存在，无需初始化")
            return {"status": "success", "message": "PromptGuoduYunjing结构已存在，无需初始化"}
        
    except Exception as e:
        error_msg = f"PromptGuoduYunjing初始化失败: {str(e)}"
        print(f"[Excel] {error_msg}")
        api_logger.error(error_msg)
        return {"status": "error", "message": error_msg}

def write_transition_camera_prompt_to_sheet3(transition_prompt: str, page_id: int, project_id: str = None) -> dict:
    """
    将过渡运镜提示词写入Excel文件的PromptGuoduYunjing表格
    
    Args:
        transition_prompt: 过渡运镜提示词内容
        page_id: 当前页码（1-N）
        project_id: 项目ID，如果为None则从PromptAll获取最新的Project_ID
    
    Returns:
        dict: 写入结果
    """
    try:
        print(f"[PromptGuoduYunjing写入] 开始写入过渡运镜提示词...")
        api_logger.info(f"开始写入过渡运镜提示词到PromptGuoduYunjing")
        
        # 记录输入参数
        print(f"[PromptGuoduYunjing参数] 页码ID: {page_id}, 项目ID: {project_id}, 提示词长度: {len(transition_prompt)}")
        api_logger.info(f"PromptGuoduYunjing写入参数 - 页码ID: {page_id}, 项目ID: {project_id}, 提示词长度: {len(transition_prompt)}")
        
        if load_workbook is None:
            error_msg = "openpyxl库未安装"
            print(f"[PromptGuoduYunjing错误] {error_msg}")
            api_logger.error(f"PromptGuoduYunjing写入失败: {error_msg}")
            return {"status": "error", "message": error_msg}
        
        excel_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "create_image_prompt.xlsx")
        print(f"[PromptGuoduYunjing文件] Excel路径: {excel_path}")
        api_logger.info(f"PromptGuoduYunjing操作Excel文件: {excel_path}")
        
        if not os.path.exists(excel_path):
            error_msg = f"Excel文件不存在: {excel_path}"
            print(f"[PromptGuoduYunjing错误] {error_msg}")
            api_logger.error(f"PromptGuoduYunjing写入失败: {error_msg}")
            return {"status": "error", "message": error_msg}
        
        # 加载工作簿
        print(f"[PromptGuoduYunjing操作] 正在加载Excel工作簿...")
        api_logger.info(f"正在加载Excel工作簿")
        workbook = load_workbook(excel_path)
        
        # 获取或创建PromptGuoduYunjing
        if 'PromptGuoduYunjing' not in workbook.sheetnames:
            print(f"[PromptGuoduYunjing操作] PromptGuoduYunjing不存在，正在创建...")
            api_logger.info(f"PromptGuoduYunjing不存在，正在创建新的PromptGuoduYunjing")
            sheet3 = workbook.create_sheet('PromptGuoduYunjing')
            # 初始化表头
            sheet3['A1'] = 'Number'
            sheet3['B1'] = 'Project_ID'
            sheet3['C1'] = 'Page_ID'
            sheet3['D1'] = 'Page_Guodu_Prompt'
            print(f"[PromptGuoduYunjing操作] PromptGuoduYunjing创建完成，表头已初始化")
            api_logger.info(f"PromptGuoduYunjing创建完成，表头已初始化")
        else:
            print(f"[PromptGuoduYunjing操作] PromptGuoduYunjing已存在，正在使用现有表格")
            api_logger.info(f"PromptGuoduYunjing已存在，正在使用现有表格")
            sheet3 = workbook['PromptGuoduYunjing']
        
        # 如果没有提供project_id，从StoryList和PromptAll获取最新的Project_ID和总页数
        total_pages = 8  # 默认页数
        if project_id is None:
            print(f"[PromptGuoduYunjing查找] 未提供项目ID，正在从StoryList和PromptAll获取...")
            api_logger.info(f"未提供项目ID，正在从StoryList和PromptAll获取最新Project_ID")
            
            # 优先从StoryList获取最新的Project_ID
            if 'StoryList' in workbook.sheetnames:
                storylist_sheet = workbook['StoryList']
                # 智能查找StoryList的最后一个非空行
                last_storylist_row = 1
                for row in range(1, storylist_sheet.max_row + 1):
                    if storylist_sheet[f'A{row}'].value is not None:
                        last_storylist_row = row
                
                if last_storylist_row > 1:  # 确保有数据行
                    project_id = storylist_sheet[f'B{last_storylist_row}'].value
                    print(f"[PromptGuoduYunjing查找] 从StoryList获取到项目ID: {project_id}，行号: {last_storylist_row}")
                    api_logger.info(f"从StoryList获取到项目ID: {project_id}，行号: {last_storylist_row}")
                    
                    # 尝试从PromptAll获取总页数
                    if 'PromptAll' in workbook.sheetnames:
                        promptall_sheet = workbook['PromptAll']
                        for row in range(2, promptall_sheet.max_row + 1):
                            if promptall_sheet[f'B{row}'].value == project_id:
                                pages_value = promptall_sheet[f'C{row}'].value
                                if isinstance(pages_value, int):
                                    total_pages = pages_value
                                print(f"[PromptGuoduYunjing查找] 从PromptAll获取总页数: {total_pages}")
                                api_logger.info(f"从PromptAll获取总页数: {total_pages}")
                                break
            
            # 如果StoryList中没有找到，再尝试从PromptAll获取
            if project_id is None and 'PromptAll' in workbook.sheetnames:
                sheet1 = workbook['PromptAll']
                # 智能查找PromptAll的最后一个非空行
                last_row = 1
                for row in range(1, sheet1.max_row + 1):
                    if sheet1[f'B{row}'].value is not None:
                        last_row = row
                if last_row > 1:
                    project_id = sheet1[f'B{last_row}'].value
                    pages_value = sheet1[f'C{last_row}'].value
                    if isinstance(pages_value, int):
                        total_pages = pages_value
                    print(f"[PromptGuoduYunjing查找] 从PromptAll获取到项目ID: {project_id}，总页数: {total_pages}，行号: {last_row}")
                    api_logger.info(f"从PromptAll获取到项目ID: {project_id}，总页数: {total_pages}，行号: {last_row}")
            
            if project_id is None:
                error_msg = "无法获取Project_ID，请先生成故事内容"
                print(f"[PromptGuoduYunjing错误] {error_msg}")
                api_logger.error(f"PromptGuoduYunjing写入失败: {error_msg}")
                return {"status": "error", "message": error_msg}
        
        # 初始化PromptGuoduYunjing结构
        print(f"[PromptGuoduYunjing初始化] 开始初始化结构，项目ID: {project_id}，总页数: {total_pages}")
        api_logger.info(f"开始初始化PromptGuoduYunjing结构，项目ID: {project_id}，总页数: {total_pages}")
        
        # 先关闭当前工作簿，让初始化函数重新加载
        workbook.close()
        
        init_result = initialize_sheet3_structure(project_id, total_pages)
        if init_result["status"] == "success":
            print(f"[成功] PromptGuoduYunjing结构初始化: {init_result['message']}")
            api_logger.info(f"PromptGuoduYunjing结构初始化: {init_result['message']}")
        else:
            print(f"[警告] PromptGuoduYunjing初始化失败: {init_result['message']}")
            api_logger.warning(f"PromptGuoduYunjing初始化失败: {init_result['message']}")
        
        # 重新加载工作簿以获取最新状态
        workbook = load_workbook(excel_path)
        sheet3 = workbook['PromptGuoduYunjing']
        
        # 查找是否已存在相同Project_ID和Page_ID的行
        print(f"[PromptGuoduYunjing查找] 正在查找现有记录，项目ID: {project_id}, 页码ID: {page_id}")
        api_logger.info(f"正在查找现有记录，项目ID: {project_id}, 页码ID: {page_id}")
        target_row = None
        for row in range(2, sheet3.max_row + 1):
            if (sheet3[f'B{row}'].value == project_id and 
                sheet3[f'C{row}'].value == page_id):
                target_row = row
                print(f"[PromptGuoduYunjing查找] 找到现有记录，行号: {target_row}")
                api_logger.info(f"找到现有记录，行号: {target_row}")
                break
        
        # 如果没有找到，创建新行
        if target_row is None:
            print(f"[PromptGuoduYunjing创建] 未找到现有记录，正在创建新行...")
            api_logger.info(f"未找到现有记录，正在创建新行")
            # 找到PromptGuoduYunjing的最后一行
            last_row = 1
            for row in range(1, sheet3.max_row + 1):
                if sheet3[f'A{row}'].value is not None:
                    last_row = row
            
            target_row = last_row + 1
            print(f"[PromptGuoduYunjing创建] 新行号: {target_row}，上一行: {last_row}")
            api_logger.info(f"新行号: {target_row}，上一行: {last_row}")
            
            # 设置Number（自增长）
            if last_row == 1 and sheet3['A1'].value == 'Number':
                new_number = 1
            else:
                last_number = sheet3[f'A{last_row}'].value
                new_number = (last_number + 1) if isinstance(last_number, int) else 1
            
            print(f"[PromptGuoduYunjing创建] 设置编号: {new_number}")
            api_logger.info(f"设置编号: {new_number}")
            
            sheet3[f'A{target_row}'] = new_number
            sheet3[f'B{target_row}'] = project_id
            sheet3[f'C{target_row}'] = page_id
        
        # 写入过渡运镜提示词
        print(f"[PromptGuoduYunjing写入] 正在写入过渡运镜提示词到行 {target_row}...")
        api_logger.info(f"正在写入过渡运镜提示词到行 {target_row}")
        sheet3[f'D{target_row}'] = transition_prompt
        
        # 保存文件
        print(f"[PromptGuoduYunjing保存] 正在保存Excel文件...")
        api_logger.info(f"正在保存Excel文件")
        workbook.save(excel_path)
        workbook.close()
        print(f"[PromptGuoduYunjing保存] Excel文件保存完成")
        api_logger.info(f"Excel文件保存完成")
        
        success_msg = f"过渡运镜提示词已写入PromptGuoduYunjing，行号: {target_row}, Project_ID: {project_id}, Page_ID: {page_id}"
        print(f"[PromptGuoduYunjing成功] {success_msg}")
        api_logger.info(success_msg)
        
        return {"status": "success", "message": f"已写入PromptGuoduYunjing第{target_row}行，Project_ID: {project_id}, Page_ID: {page_id}"}
        
    except Exception as e:
        error_msg = f"PromptGuoduYunjing写入失败: {str(e)}"
        print(f"[PromptGuoduYunjing错误] {error_msg}")
        print(f"[PromptGuoduYunjing错误] 异常类型: {type(e).__name__}")
        print(f"[PromptGuoduYunjing错误] 异常详情: {str(e)}")
        api_logger.error(f"PromptGuoduYunjing写入异常 - 类型: {type(e).__name__}, 详情: {str(e)}")
        api_logger.error(error_msg)
        return {"status": "error", "message": error_msg}

def initialize_sheet2_structure(project_id: str, total_pages: int) -> dict:
    """
    智能初始化PromptYunjing的内容结构
    
    Args:
        project_id: 项目ID
        total_pages: 总页数
    
    Returns:
        dict: 初始化结果
    """
    try:
        if load_workbook is None:
            return {"status": "error", "message": "openpyxl库未安装"}
        
        excel_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "create_image_prompt.xlsx")
        
        if not os.path.exists(excel_path):
            return {"status": "error", "message": f"Excel文件不存在: {excel_path}"}
        
        # 加载工作簿
        print(f"[PromptYunjing操作] 正在加载Excel工作簿...")
        api_logger.info(f"正在加载Excel工作簿")
        workbook = load_workbook(excel_path)
        
        # 获取或创建PromptYunjing
        if 'PromptYunjing' not in workbook.sheetnames:
            print(f"[PromptYunjing操作] PromptYunjing不存在，正在创建...")
            api_logger.info(f"PromptYunjing不存在，正在创建新的PromptYunjing")
            sheet2 = workbook.create_sheet('PromptYunjing')
            # 初始化表头
            sheet2['A1'] = 'Number'
            sheet2['B1'] = 'Project_ID'
            sheet2['C1'] = 'Page_ID'
            sheet2['D1'] = 'Page_prompt'
            print(f"[PromptYunjing操作] PromptYunjing创建完成，表头已初始化")
            api_logger.info(f"PromptYunjing创建完成，表头已初始化")
        else:
            print(f"[PromptYunjing操作] PromptYunjing已存在，正在使用现有表格")
            api_logger.info(f"PromptYunjing已存在，正在使用现有表格")
            sheet2 = workbook['PromptYunjing']
        
        # 检查是否已经存在该Project_ID的记录（防止重复初始化）
        existing_project_records = []
        for row in range(2, sheet2.max_row + 1):
            if sheet2[f'B{row}'].value == project_id:
                existing_project_records.append(row)
        
        if existing_project_records:
            error_msg = f"Project_ID {project_id} 已存在于PromptYunjing表格中，不允许重复初始化"
            print(f"[PromptYunjing错误] {error_msg}")
            api_logger.error(error_msg)
            return {"status": "error", "message": error_msg}
        
        print(f"[PromptYunjing操作] 开始为Project_ID: {project_id} 初始化 {total_pages} 页的结构")
        api_logger.info(f"开始为Project_ID: {project_id} 初始化 {total_pages} 页的结构")
        
        # 智能查找最后一个非空行
        last_row = 1
        for row in range(1, sheet2.max_row + 1):
            if sheet2[f'A{row}'].value is not None and sheet2[f'A{row}'].value != '':
                last_row = row
        
        print(f"[PromptYunjing操作] 找到最后一个非空行: {last_row}")
        api_logger.info(f"找到最后一个非空行: {last_row}")
        
        # 在最后一个非空行的下一行开始写入（页码数）行
        start_row = last_row + 1
        for page_id in range(1, total_pages + 1):
            new_row = start_row + page_id - 1
            
            # 设置Number（自增长）
            if last_row == 1 and sheet2['A1'].value == 'Number':
                new_number = page_id
            else:
                last_number = sheet2[f'A{last_row}'].value if page_id == 1 else sheet2[f'A{new_row-1}'].value
                new_number = (last_number + page_id) if isinstance(last_number, int) and page_id == 1 else (last_number + 1) if isinstance(last_number, int) else page_id
            
            sheet2[f'A{new_row}'] = new_number
            sheet2[f'B{new_row}'] = project_id  # Project_ID_all
            sheet2[f'C{new_row}'] = page_id     # Page_ID从1到页码数
            sheet2[f'D{new_row}'] = ''          # Page_Prompt暂时为空
            
            print(f"[PromptYunjing创建] 第{new_row}行: Number={new_number}, Project_ID={project_id}, Page_ID={page_id}")
            api_logger.info(f"第{new_row}行: Number={new_number}, Project_ID={project_id}, Page_ID={page_id}")
        
        rows_added = total_pages
        
        # 保存文件
        workbook.save(excel_path)
        workbook.close()
        
        if rows_added > 0:
            print(f"[Excel] PromptYunjing结构初始化完成，为Project_ID: {project_id} 添加了 {rows_added} 行")
            api_logger.info(f"PromptYunjing结构初始化完成，为Project_ID: {project_id} 添加了 {rows_added} 行")
            return {"status": "success", "message": f"PromptYunjing结构初始化完成，添加了 {rows_added} 行"}
        else:
            return {"status": "success", "message": "PromptYunjing结构已存在，无需初始化"}
        
    except Exception as e:
        error_msg = f"PromptYunjing初始化失败: {str(e)}"
        print(f"[Excel] {error_msg}")
        api_logger.error(error_msg)
        return {"status": "error", "message": error_msg}

def clean_text_content(content: str) -> str:
    """
    清理文本内容，移除HTML标签、CSS样式和其他非文本内容
    
    Args:
        content: 原始内容
    
    Returns:
        str: 清理后的纯文本内容
    """
    import re
    
    # 移除CSS样式块（包括<style>标签和内联样式）
    content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL | re.IGNORECASE)
    
    # 移除CSS样式规则（独立的CSS代码块）
    content = re.sub(r'\s*[a-zA-Z0-9\-_\.#\s,]+\s*\{[^}]*\}', '', content, flags=re.DOTALL)
    
    # 移除所有HTML标签
    content = re.sub(r'<[^>]+>', '', content)
    
    # 移除多余的空白字符和换行
    content = re.sub(r'\s+', ' ', content)
    
    # 移除首尾空白
    content = content.strip()
    
    return content

def write_story_to_storylist(html_content: str, txt_content: str, project_id: str = None) -> dict:
    """
    将LLM返回的故事内容写入Excel文件的StoryList表格
    
    Args:
        html_content: HTML格式的故事内容
        txt_content: 纯文本格式的故事内容（将被进一步清理）
        project_id: 项目ID，如果为None则生成新的Project_ID
    
    Returns:
        dict: 写入结果
    """
    try:
        import traceback
        call_stack = traceback.format_stack()
        print(f"[StoryList写入] 开始写入故事内容... 调用栈: {len(call_stack)} 层")
        api_logger.info(f"开始写入故事内容到StoryList，调用栈深度: {len(call_stack)}")
        
        # 记录详细的调用信息
        caller_info = call_stack[-2] if len(call_stack) >= 2 else "未知调用者"
        print(f"[StoryList调用者] {caller_info.strip()}")
        api_logger.info(f"StoryList调用者信息: {caller_info.strip()}")
        
        # 记录输入参数
        print(f"[StoryList参数] 项目ID: {project_id}, HTML长度: {len(html_content)}, 文本长度: {len(txt_content)}")
        api_logger.info(f"StoryList写入参数 - 项目ID: {project_id}, HTML长度: {len(html_content)}, 文本长度: {len(txt_content)}")
        
        if load_workbook is None:
            error_msg = "openpyxl库未安装"
            print(f"[StoryList错误] {error_msg}")
            api_logger.error(error_msg)
            return {"status": "error", "message": error_msg}
        
        excel_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "create_image_prompt.xlsx")
        
        if not os.path.exists(excel_path):
            error_msg = f"Excel文件不存在: {excel_path}"
            print(f"[StoryList错误] {error_msg}")
            api_logger.error(error_msg)
            return {"status": "error", "message": error_msg}
        
        # 加载工作簿
        print(f"[StoryList操作] 正在加载Excel工作簿...")
        api_logger.info(f"正在加载Excel工作簿")
        workbook = load_workbook(excel_path)
        
        # 检查或创建StoryList工作表
        if 'StoryList' not in workbook.sheetnames:
            print(f"[StoryList操作] 创建StoryList工作表...")
            api_logger.info(f"创建StoryList工作表")
            worksheet = workbook.create_sheet('StoryList')
            
            # 设置表头
            worksheet['A1'] = 'Number'
            worksheet['B1'] = 'Project_ID'
            worksheet['C1'] = 'Html_content'
            worksheet['D1'] = 'Txt_content'
            
            print(f"[StoryList操作] StoryList工作表创建完成，已设置表头")
            api_logger.info(f"StoryList工作表创建完成，已设置表头")
        else:
            worksheet = workbook['StoryList']
            print(f"[StoryList操作] 找到现有StoryList工作表")
            api_logger.info(f"找到现有StoryList工作表")
            
            # 检查是否已存在相同的Project_ID（写入计数器限制）
            if project_id:
                for row in range(2, worksheet.max_row + 1):
                    existing_project_id = worksheet.cell(row=row, column=2).value
                    if existing_project_id == project_id:
                        error_msg = f"Project_ID {project_id} 已存在，StoryList写入计数器限制，不允许重复写入"
                        print(f"[StoryList错误] {error_msg}")
                        api_logger.error(error_msg)
                        return {"status": "error", "message": error_msg}
        
        # 智能查找最后一个非空行（从Number列开始验证）
        last_row = 1
        for row in range(1, worksheet.max_row + 1):
            if worksheet[f'A{row}'].value is not None and worksheet[f'A{row}'].value != '':
                last_row = row
        
        print(f"[StoryList智能查找] 找到最后一个非空行: {last_row}")
        api_logger.info(f"StoryList智能查找到最后一个非空行: {last_row}")
        
        # 在最后一个非空行的下一行添加新数据
        new_row = last_row + 1
        
        print(f"[StoryList写入] 将在第{new_row}行添加新数据")
        api_logger.info(f"StoryList将在第{new_row}行添加新数据")
        
        # 第一列"Number"：自增长数值型
        if last_row == 1 and worksheet['A1'].value == 'Number':
            # 如果是只有表头的表格，从1开始
            new_number = 1
        else:
            # 获取最后一行的Number值并加1
            last_number = worksheet[f'A{last_row}'].value
            new_number = (last_number + 1) if isinstance(last_number, int) else 1
        
        worksheet[f'A{new_row}'] = new_number
        
        # 第二列"Project_ID"：必须提供有效的项目ID
        if project_id is None:
            error_msg = "必须提供有效的Project_ID，不允许自动生成"
            print(f"[StoryList错误] {error_msg}")
            api_logger.error(error_msg)
            return {"status": "error", "message": error_msg}
            
        print(f"[StoryList操作] 使用提供的Project_ID: {project_id}")
        api_logger.info(f"使用提供的Project_ID: {project_id}")
        
        worksheet[f'B{new_row}'] = project_id  # Project_ID_all
        
        # 第三列"Html_content"：HTML格式的故事内容
        worksheet[f'C{new_row}'] = html_content
        
        # 第四列"Txt_content"：清理后的纯文本格式的故事内容
        cleaned_txt_content = clean_text_content(txt_content)
        worksheet[f'D{new_row}'] = cleaned_txt_content
        
        print(f"[StoryList文本清理] 原始长度: {len(txt_content)}, 清理后长度: {len(cleaned_txt_content)}")
        api_logger.info(f"StoryList文本清理 - 原始长度: {len(txt_content)}, 清理后长度: {len(cleaned_txt_content)}")
        
        # 保存文件
        print(f"[StoryList操作] 正在保存Excel文件...")
        api_logger.info(f"正在保存Excel文件")
        workbook.save(excel_path)
        workbook.close()
        
        print(f"[StoryList成功] 故事内容已写入StoryList，行号: {new_row}")
        print(f"[StoryList成功] Number: {new_number}, Project_ID: {project_id}")
        print(f"[StoryList成功] 文本内容已清理，移除CSS样式和HTML标签")
        print(f"[StoryList计数器] Project_ID {project_id} 写入完成，写入计数器已设置为1，不允许再次写入")
        api_logger.info(f"故事内容已写入StoryList，行号: {new_row}, Number: {new_number}, Project_ID: {project_id}")
        api_logger.info(f"文本内容已清理，移除CSS样式和HTML标签")
        api_logger.info(f"Project_ID {project_id} 写入完成，写入计数器已设置为1")
        
        return {
            "status": "success", 
            "message": f"已写入第{new_row}行，Number: {new_number}, Project_ID: {project_id}，文本已清理",
            "project_id": project_id,
            "number": new_number,
            "row": new_row,
            "cleaned_text_length": len(cleaned_txt_content)
        }
        
    except Exception as e:
        error_msg = f"StoryList写入失败: {str(e)}"
        print(f"[StoryList错误] {error_msg}")
        api_logger.error(error_msg)
        return {"status": "error", "message": error_msg}

def write_to_excel(summary_prompt: str, total_pages: int = 8, project_id: str = None) -> dict:
    """
    将汇总提示词写入Excel文件
    
    Args:
        summary_prompt: 汇总提示词内容
        total_pages: 当前故事的总页数，默认为8
        project_id: 关联的项目ID，如果未提供则从StoryList表格的最后一行获取
    
    Returns:
        dict: 写入结果
    """
    try:
        import uuid
        import datetime
        
        if load_workbook is None:
            return {"status": "error", "message": "openpyxl库未安装"}
        
        excel_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "create_image_prompt.xlsx")
        
        if not os.path.exists(excel_path):
            return {"status": "error", "message": f"Excel文件不存在: {excel_path}"}
        
        # 加载工作簿
        workbook = load_workbook(excel_path)
        
        # 如果未提供project_id，从StoryList表格的最后一行获取
        if not project_id:
            print(f"[PromptAll] 未提供Project_ID，正在从StoryList表格获取...")
            api_logger.info(f"未提供Project_ID，正在从StoryList表格获取")
            
            if 'StoryList' in workbook.sheetnames:
                storylist_sheet = workbook['StoryList']
                
                # 智能查找StoryList最后一个非空行
                last_storylist_row = 1
                for row in range(1, storylist_sheet.max_row + 1):
                    if storylist_sheet[f'A{row}'].value is not None and storylist_sheet[f'A{row}'].value != '':
                        last_storylist_row = row
                
                if last_storylist_row > 1:  # 确保不是表头行
                    project_id = storylist_sheet[f'B{last_storylist_row}'].value
                    print(f"[PromptAll] 从StoryList第{last_storylist_row}行获取到Project_ID: {project_id}")
                    api_logger.info(f"从StoryList第{last_storylist_row}行获取到Project_ID: {project_id}")
                else:
                    return {"status": "error", "message": "StoryList表格中没有找到有效的Project_ID"}
            else:
                return {"status": "error", "message": "StoryList表格不存在，无法获取Project_ID"}
        
        # 获取PromptAll工作表
        if 'PromptAll' not in workbook.sheetnames:
            return {"status": "error", "message": "PromptAll工作表不存在"}
        
        worksheet = workbook['PromptAll']
        
        # 检查是否已存在相同的Project_ID（写入计数器限制）
        if project_id:
            for row in range(2, worksheet.max_row + 1):
                existing_project_id = worksheet.cell(row=row, column=2).value
                if existing_project_id == project_id:
                    error_msg = f"Project_ID {project_id} 已存在，PromptAll写入计数器限制，不允许重复写入"
                    print(f"[PromptAll错误] {error_msg}")
                    api_logger.error(error_msg)
                    return {"status": "error", "message": error_msg}
        
        # 智能查找最后一个非空行（从Number列开始验证）
        last_row = 1
        for row in range(1, worksheet.max_row + 1):
            if worksheet[f'A{row}'].value is not None and worksheet[f'A{row}'].value != '':
                last_row = row
        
        print(f"[PromptAll智能查找] 找到最后一个非空行: {last_row}")
        api_logger.info(f"PromptAll智能查找到最后一个非空行: {last_row}")
        
        # 在最后一个非空行的下一行添加新数据
        new_row = last_row + 1
        
        print(f"[PromptAll写入] 将在第{new_row}行添加新数据")
        api_logger.info(f"PromptAll将在第{new_row}行添加新数据")
        
        # 第一列"Number"：自增长数值型
        if last_row == 1 and worksheet['A1'].value is None:
            # 如果是空表格，从1开始
            new_id = 1
        else:
            # 获取最后一行的ID值并加1
            last_id = worksheet[f'A{last_row}'].value
            new_id = (last_id + 1) if isinstance(last_id, int) else 1
        
        worksheet[f'A{new_row}'] = new_id
        
        # 第二列"Project_ID"：使用获取到的project_id
        worksheet[f'B{new_row}'] = project_id  # Project_ID_all
        
        # 第三列"Pages"：当前故事的总页数
        worksheet[f'C{new_row}'] = total_pages
        
        # 第四列"Prompt_all"：汇总提示词
        worksheet[f'D{new_row}'] = summary_prompt
        
        # 保存文件
        workbook.save(excel_path)
        workbook.close()
        
        print(f"[Excel] 汇总提示词已写入Excel文件，行号: {new_row}")
        print(f"[Excel] Number: {new_id}, Project_ID: {project_id}, Pages: {total_pages}")
        print(f"[PromptAll计数器] Project_ID {project_id} 写入完成，写入计数器已设置为1，不允许再次写入")
        api_logger.info(f"汇总提示词已写入Excel文件，行号: {new_row}, Number: {new_id}, Project_ID: {project_id}, Pages: {total_pages}")
        api_logger.info(f"Project_ID {project_id} 写入完成，写入计数器已设置为1")
        
        return {"status": "success", "message": f"已写入第{new_row}行，Number: {new_id}, Project_ID: {project_id}, Pages: {total_pages}"}
        
    except Exception as e:
        error_msg = f"Excel写入失败: {str(e)}"
        print(f"[Excel] {error_msg}")
        api_logger.error(error_msg)
        return {"status": "error", "message": error_msg}

@app.route('/generate', methods=['POST'])
def api_generate():
    """API endpoint for generation"""
    try:
        data = request.json
        if not data:
            return jsonify({'status': 'error', 'message': 'No data provided'})

        prompt = data.get('prompt', '')
        if not prompt:
            return jsonify({'status': 'error', 'message': 'No prompt provided'})

        model = data.get('model', GEMINI_FLASH_MODEL)  # 默认模型
        request_type = data.get('request_type', '')  # 获取请求类型
        
        # 检测是否为汇总提示词生成请求
        is_summary = ("汇总提示词" in prompt or "连环画绘本故事" in prompt or "连续镜头" in prompt)
        
        # 检测是否为运镜提示词生成请求
        is_camera_prompt = (request_type == 'camera_prompt_plain_text')
        
        # 检测是否为过渡运镜提示词生成请求
        is_transition_camera_prompt = (request_type == 'transition_camera_prompt_plain_text')
        
        # 记录请求详细信息
        print(f"[API请求] 请求类型: {request_type}, 是否汇总: {is_summary}, 是否运镜: {is_camera_prompt}, 是否过渡运镜: {is_transition_camera_prompt}")
        api_logger.info(f"API请求详情 - 类型: {request_type}, 汇总: {is_summary}, 运镜: {is_camera_prompt}, 过渡运镜: {is_transition_camera_prompt}")
        
        if is_camera_prompt:
            page_id = data.get('page_id', 1)
            print(f"[运镜请求] 页码ID: {page_id}, 原始提示词长度: {len(prompt)}")
            api_logger.info(f"运镜请求详情 - 页码: {page_id}, 原始提示词长度: {len(prompt)}")
            
            # 使用config.py中的build_camera_prompt函数构建完整的运镜提示词
            full_camera_prompt = build_camera_prompt(prompt)
            prompt = full_camera_prompt
            print(f"[运镜构建] 完整提示词长度: {len(prompt)}")
            api_logger.info(f"运镜提示词构建完成 - 完整长度: {len(prompt)}")
        
        elif is_transition_camera_prompt:
            page_id = data.get('page_id', 1)
            next_page_id = data.get('next_page_id', page_id + 1)
            print(f"[过渡运镜请求] 当前页码ID: {page_id}, 下一页码ID: {next_page_id}, 提示词长度: {len(prompt)}")
            api_logger.info(f"过渡运镜请求详情 - 当前页码: {page_id}, 下一页码: {next_page_id}, 提示词长度: {len(prompt)}")
            # 过渡运镜提示词已经在前端构建完成，直接使用

        max_keys_to_try = len(API_KEYS) - len(INVALID_API_KEYS)
        keys_tried = 0

        while keys_tried < max_keys_to_try:
            try:
                api_key = get_next_api_key()
                result = generate_with_key(prompt, api_key, model, is_summary=(is_summary or is_camera_prompt))

                if result["status"] == "success":
                    # 如果是汇总提示词生成成功，只写入Excel文件PromptAll
                    if is_summary and result.get("results") and len(result["results"]) > 0:
                        summary_content = result["results"][0].get("content", "")
                        if summary_content:
                            # 从请求数据中获取总页数，默认为8
                            total_pages = data.get('total_pages', 8)
                            
                            # 如果前端没有传递total_pages，尝试从prompt中提取页数
                            if total_pages == 8 and 'total_pages' not in data:
                                import re
                                page_match = re.search(r'故事的(\d+)页连续镜头', prompt)
                                if page_match:
                                    total_pages = int(page_match.group(1))
                                else:
                                    # 尝试从prompt中查找其他页数模式
                                    page_match2 = re.search(r'(\d+)页.*故事', prompt)
                                    if page_match2:
                                        total_pages = int(page_match2.group(1))
                            
                            # 获取已有的project_id，不生成新ID
                            # 从StoryList获取最新的project_id
                            try:
                                excel_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "create_image_prompt.xlsx")
                                workbook = load_workbook(excel_path)
                                if 'StoryList' in workbook.sheetnames:
                                    storylist_sheet = workbook['StoryList']
                                    last_storylist_row = 1
                                    for row in range(1, storylist_sheet.max_row + 1):
                                        if storylist_sheet[f'A{row}'].value is not None and storylist_sheet[f'A{row}'].value != '':
                                            last_storylist_row = row
                                    if last_storylist_row > 1:
                                        project_id = storylist_sheet[f'B{last_storylist_row}'].value
                                        print(f"[汇总处理] 从StoryList获取到项目ID: {project_id}")
                                        api_logger.info(f"从StoryList获取到项目ID: {project_id}")
                                    else:
                                        print(f"[警告] StoryList中没有找到有效的Project_ID")
                                        api_logger.warning(f"StoryList中没有找到有效的Project_ID")
                                        return jsonify({"status": "error", "message": "StoryList中没有找到有效的Project_ID"})
                                else:
                                    print(f"[警告] StoryList表格不存在")
                                    api_logger.warning(f"StoryList表格不存在")
                                    return jsonify({"status": "error", "message": "StoryList表格不存在"})
                                workbook.close()
                            except Exception as e:
                                print(f"[警告] 获取Project_ID失败: {str(e)}")
                                api_logger.warning(f"获取Project_ID失败: {str(e)}")
                                return jsonify({"status": "error", "message": f"获取Project_ID失败: {str(e)}"})
                            
                            print(f"[汇总处理] 开始处理汇总提示词，项目ID: {project_id}")
                            api_logger.info(f"开始处理汇总提示词，项目ID: {project_id}")
                            
                            # 直接写入PromptAll表格（汇总提示词已经是纯文本，直接写入Excel PromptAll）
                            excel_result = write_to_excel(summary_content.strip(), total_pages, project_id)
                            if excel_result["status"] == "success":
                                print(f"[成功] 汇总提示词已写入Excel PromptAll: {excel_result['message']}")
                                api_logger.info(f"汇总提示词已写入Excel PromptAll: {excel_result['message']}")
                                
                                # 汇总提示词写入成功后，初始化Sheet2结构
                                init_result = initialize_sheet2_structure(project_id, total_pages)
                                if init_result["status"] == "success":
                                    print(f"[成功] PromptYunjing结构初始化: {init_result['message']}")
                                    api_logger.info(f"PromptYunjing结构初始化: {init_result['message']}")
                                else:
                                    print(f"[警告] PromptYunjing初始化失败: {init_result['message']}")
                                    api_logger.warning(f"PromptYunjing初始化失败: {init_result['message']}")
                            else:
                                print(f"[警告] Excel写入失败: {excel_result['message']}")
                                api_logger.warning(f"Excel写入失败: {excel_result['message']}")
                    
                    # 如果是运镜提示词生成成功，写入Excel文件PromptYunjing
                    elif is_camera_prompt and result.get("results") and len(result["results"]) > 0:
                        print(f"[运镜处理] 开始处理运镜提示词生成结果...")
                        api_logger.info(f"开始处理运镜提示词生成结果")
                        
                        camera_content = result["results"][0].get("content", "")
                        print(f"[运镜内容] 原始内容长度: {len(camera_content)}")
                        api_logger.info(f"运镜原始内容长度: {len(camera_content)}")
                        
                        if camera_content:
                            # 从请求数据中获取页码ID
                            page_id = data.get('page_id', 1)
                            print(f"[运镜参数] 页码ID: {page_id}")
                            api_logger.info(f"运镜处理参数 - 页码ID: {page_id}")
                            
                            # 清理运镜提示词内容，移除多余的前缀
                            cleaned_content = camera_content.strip()
                            original_length = len(cleaned_content)
                            if cleaned_content.startswith('以这张图片为首镜头，生成一个多机位视频，'):
                                cleaned_content = cleaned_content[len('以这张图片为首镜头，生成一个多机位视频，'):].strip()
                                print(f"[运镜清理] 移除前缀，内容长度: {original_length} -> {len(cleaned_content)}")
                                api_logger.info(f"运镜内容清理 - 移除前缀，长度变化: {original_length} -> {len(cleaned_content)}")
                            else:
                                print(f"[运镜清理] 无需移除前缀，内容长度: {len(cleaned_content)}")
                                api_logger.info(f"运镜内容清理 - 无需移除前缀，长度: {len(cleaned_content)}")
                            
                            # 写入PromptYunjing
                            print(f"[运镜写入] 开始写入Excel PromptYunjing...")
                            api_logger.info(f"开始写入运镜提示词到Excel PromptYunjing")
                            
                            # 在运镜提示词前添加固定前缀
                            prefixed_camera_prompt = "以这张图片为首镜头，生成一个多机位视频，" + cleaned_content
                            sheet2_result = write_camera_prompt_to_sheet2(prefixed_camera_prompt, page_id)
                            if sheet2_result["status"] == "success":
                                print(f"[成功] 运镜提示词已写入Excel PromptYunjing: {sheet2_result['message']}")
                                api_logger.info(f"运镜提示词已写入Excel PromptYunjing: {sheet2_result['message']}")
                            else:
                                print(f"[错误] PromptYunjing写入失败: {sheet2_result['message']}")
                                api_logger.error(f"PromptYunjing写入失败: {sheet2_result['message']}")
                        else:
                            print(f"[错误] 运镜提示词内容为空")
                            api_logger.error(f"运镜提示词内容为空，无法写入Excel")
                    elif is_camera_prompt:
                        print(f"[错误] 运镜提示词生成失败或结果为空")
                        api_logger.error(f"运镜提示词生成失败或结果为空 - 结果: {result}")
                    
                    # 如果是过渡运镜提示词生成成功，写入Excel文件PromptGuoduYunjing
                    elif is_transition_camera_prompt and result.get("results") and len(result["results"]) > 0:
                        print(f"[过渡运镜处理] 开始处理过渡运镜提示词生成结果...")
                        api_logger.info(f"开始处理过渡运镜提示词生成结果")
                        
                        transition_content = result["results"][0].get("content", "")
                        print(f"[过渡运镜内容] 原始内容长度: {len(transition_content)}")
                        api_logger.info(f"过渡运镜原始内容长度: {len(transition_content)}")
                        
                        if transition_content:
                            # 从请求数据中获取页码ID
                            page_id = data.get('page_id', 1)
                            print(f"[过渡运镜参数] 页码ID: {page_id}")
                            api_logger.info(f"过渡运镜处理参数 - 页码ID: {page_id}")
                            
                            # 清理过渡运镜提示词内容，移除多余的前缀
                            cleaned_content = transition_content.strip()
                            original_length = len(cleaned_content)
                            # 检查并移除可能存在的HTML标签
                            cleaned_content = clean_text_content(cleaned_content)
                            if cleaned_content.startswith('以这张图片为首镜头，生成一个多机位视频，'):
                                cleaned_content = cleaned_content[len('以这张图片为首镜头，生成一个多机位视频，'):].strip()
                                print(f"[过渡运镜清理] 移除前缀，内容长度: {original_length} -> {len(cleaned_content)}")
                                api_logger.info(f"过渡运镜内容清理 - 移除前缀，长度变化: {original_length} -> {len(cleaned_content)}")
                            else:
                                print(f"[过渡运镜清理] 无需移除前缀，内容长度: {len(cleaned_content)}")
                                api_logger.info(f"过渡运镜内容清理 - 无需移除前缀，长度: {len(cleaned_content)}")
                            
                            # 在过渡运镜提示词前添加固定前缀
                            prefixed_transition_camera_prompt = "以这张图片为首镜头，生成一个多机位视频，" + cleaned_content
                            
                            # 写入PromptGuoduYunjing
                            print(f"[过渡运镜写入] 开始写入Excel PromptGuoduYunjing...")
                            api_logger.info(f"开始写入过渡运镜提示词到Excel PromptGuoduYunjing")
                            
                            sheet3_result = write_transition_camera_prompt_to_sheet3(prefixed_transition_camera_prompt, page_id)
                            if sheet3_result["status"] == "success":
                                print(f"[成功] 过渡运镜提示词已写入Excel PromptGuoduYunjing: {sheet3_result['message']}")
                                api_logger.info(f"过渡运镜提示词已写入Excel PromptGuoduYunjing: {sheet3_result['message']}")
                            else:
                                print(f"[错误] PromptGuoduYunjing写入失败: {sheet3_result['message']}")
                                api_logger.error(f"PromptGuoduYunjing写入失败: {sheet3_result['message']}")
                        else:
                            print(f"[错误] 过渡运镜提示词内容为空")
                            api_logger.error(f"过渡运镜提示词内容为空，无法写入Excel")
                    elif is_transition_camera_prompt:
                        print(f"[错误] 过渡运镜提示词生成失败或结果为空")
                        api_logger.error(f"过渡运镜提示词生成失败或结果为空 - 结果: {result}")
                    
                    # 如果是普通绘本生成成功（非汇总提示词、非运镜提示词、非过渡运镜提示词），写入StoryList
                    # 注意：汇总提示词已经在上面的分支中处理过StoryList写入，这里不再重复处理
                    elif not is_summary and not is_camera_prompt and not is_transition_camera_prompt and result.get("results") and len(result["results"]) > 0:
                        story_content = result["results"][0].get("content", "")
                        if story_content:
                            print(f"[绘本处理] 开始处理普通绘本故事内容...")
                            api_logger.info(f"开始处理普通绘本故事内容")
                            
                            # 生成统一的Project_ID_all
                            import uuid
                            import datetime
                            random_uuid = str(uuid.uuid4())
                            random_timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
                            project_id_all = f"{random_uuid}-{random_timestamp}"
                            project_id = project_id_all  # 保持兼容性
                            
                            # 获取HTML和纯文本格式的故事内容
                            html_content = story_content  # LLM返回的原始内容作为HTML
                            # 使用clean_text_content函数确保完整清理纯文本
                            txt_content = clean_text_content(story_content)
                            
                            print(f"[绘本内容] HTML长度: {len(html_content)}, 纯文本长度: {len(txt_content)}")
                            api_logger.info(f"绘本内容处理 - HTML长度: {len(html_content)}, 纯文本长度: {len(txt_content)}")
                            
                            # 写入StoryList表格
                            storylist_result = write_story_to_storylist(html_content, txt_content, project_id)
                            if storylist_result["status"] == "success":
                                print(f"[成功] 绘本故事内容已写入StoryList: {storylist_result['message']}")
                                api_logger.info(f"绘本故事内容已写入StoryList: {storylist_result['message']}")
                            else:
                                print(f"[警告] 绘本StoryList写入失败: {storylist_result['message']}")
                                api_logger.warning(f"绘本StoryList写入失败: {storylist_result['message']}")
                        else:
                            print(f"[错误] 绘本故事内容为空")
                            api_logger.error(f"绘本故事内容为空，无法写入StoryList")
                    
                    return jsonify(result)

                # If we got an error but not due to invalid key, return the error
                if "API key not valid" not in result.get("message", ""):
                    return jsonify(result)

                keys_tried += 1

            except Exception as e:
                error_msg = str(e)
                return jsonify({
                    'status': 'error',
                    'message': f'Generation failed: {error_msg}'
                })

        return jsonify({
            'status': 'error',
            'message': f'All available keys failed ({keys_tried} keys tried)'
        })
    except Exception as e:
        error_msg = str(e)
        return jsonify({
            'status': 'error',
            'message': f'Unexpected error: {error_msg}'
        })

@app.route('/get_transition_camera_prompt', methods=['POST'])
def get_transition_camera_prompt():
    """
    从Excel PromptGuoduYunjing表格中获取指定页码的过渡运镜提示词
    
    Returns:
        dict: 包含过渡运镜提示词内容的响应
    """
    try:
        data = request.get_json()
        page_id = data.get('page_id', 1)
        
        print(f"[获取过渡运镜] 请求获取页码 {page_id} 的过渡运镜提示词")
        api_logger.info(f"请求获取页码 {page_id} 的过渡运镜提示词")
        
        if load_workbook is None:
            return jsonify({"status": "error", "message": "openpyxl库未安装"})
        
        excel_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "create_image_prompt.xlsx")
        
        if not os.path.exists(excel_path):
            return jsonify({"status": "error", "message": f"Excel文件不存在: {excel_path}"})
        
        # 加载工作簿
        workbook = load_workbook(excel_path)
        
        if 'PromptGuoduYunjing' not in workbook.sheetnames:
            workbook.close()
            return jsonify({"status": "error", "message": "PromptGuoduYunjing表格不存在"})
        
        sheet3 = workbook['PromptGuoduYunjing']
        
        # 获取最新的Project_ID
        latest_project_id = None
        if 'StoryList' in workbook.sheetnames:
            storylist_sheet = workbook['StoryList']
            last_storylist_row = 1
            for row in range(1, storylist_sheet.max_row + 1):
                if storylist_sheet[f'A{row}'].value is not None and storylist_sheet[f'A{row}'].value != '':
                    last_storylist_row = row
            if last_storylist_row > 1:
                latest_project_id = storylist_sheet[f'B{last_storylist_row}'].value
        
        if latest_project_id is None:
            workbook.close()
            return jsonify({"status": "error", "message": "无法获取最新的Project_ID"})
        
        # 查找匹配的过渡运镜提示词
        transition_content = None
        for row in range(2, sheet3.max_row + 1):
            if (sheet3[f'B{row}'].value == latest_project_id and 
                sheet3[f'C{row}'].value == page_id):
                transition_content = sheet3[f'D{row}'].value
                print(f"[获取过渡运镜] 找到匹配记录，行号: {row}, Project_ID: {latest_project_id}, Page_ID: {page_id}")
                api_logger.info(f"找到匹配记录，行号: {row}, Project_ID: {latest_project_id}, Page_ID: {page_id}")
                break
        
        workbook.close()
        
        if transition_content:
            print(f"[获取过渡运镜] 成功获取过渡运镜内容，长度: {len(transition_content)}")
            api_logger.info(f"成功获取过渡运镜内容，长度: {len(transition_content)}")
            return jsonify({
                "status": "success", 
                "content": transition_content,
                "project_id": latest_project_id,
                "page_id": page_id
            })
        else:
            print(f"[获取过渡运镜] 未找到页码 {page_id} 的过渡运镜提示词")
            api_logger.warning(f"未找到页码 {page_id} 的过渡运镜提示词")
            return jsonify({"status": "error", "message": f"未找到页码 {page_id} 的过渡运镜提示词"})
            
    except Exception as e:
        error_msg = f"获取过渡运镜提示词失败: {str(e)}"
        print(f"[获取过渡运镜错误] {error_msg}")
        api_logger.error(f"获取过渡运镜提示词异常: {str(e)}")
        return jsonify({"status": "error", "message": error_msg})

@app.route('/test_api', methods=['GET'])
def test_api():
    """Test API connection (简化版，快速返回LLM响应文本)"""
    try:
        # 固定模型为gemini-2.5-flash-preview-04-17
        model = GEMINI_FLASH_MODEL
        api_key = get_next_api_key()
        if not api_key:
            return jsonify({
                'status': 'error',
                'message': 'No valid API key available'
            })
        client = genai.Client(api_key=api_key)
        prompt = "API连接测试：请回复OK"
        try:
            response = client.models.generate_content(
                model=model,
                contents=[{"role": "user", "parts": [{"text": prompt}]}]
            )
            test_text = getattr(response, "text", "")
        except Exception as e:
            test_text = str(e)
        return jsonify({
            'status': 'success',
            'test_text': test_text
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'API test failed: {str(e)}'
        })

@app.route('/temp/<filename>')
def serve_file(filename):
    """Serve temporary files"""
    return send_from_directory(temp_dir, filename)



@app.route('/generate_speech_table', methods=['POST'])
def api_generate_speech_table():
    """根据富文本HTML生成配音表格"""
    try:
        data = request.json
        html_text = data.get('html_text', '')
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr

        speech_logger.info(f"生成配音表格请求 - IP: {client_ip}, User-Agent: {user_agent}")
        speech_logger.debug(f"HTML内容长度: {len(html_text)} 字符")

        if not html_text:
            speech_logger.error("未提供HTML文本内容")
            return jsonify({'status': 'error', 'message': 'No html_text provided'})

        table_html = generate_speech_table(html_text)
        speech_logger.info(f"配音表格生成成功 - 表格行数: {table_html.count('<tr>')}")
        speech_logger.debug(f"生成的表格HTML长度: {len(table_html)} 字符")

        return jsonify({
            'status': 'success',
            'table_html': table_html,
            'log_id': str(uuid.uuid4())  # 添加日志ID用于追踪
        })
    except Exception as e:
        speech_logger.error(f"生成配音表格失败: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/generate_srt_files', methods=['POST'])
def api_generate_srt_files():
    """根据配音表HTML生成SRT文件并返回新表格"""
    try:
        data = request.json
        html_text = data.get('html_text', '')
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr
        log_id = str(uuid.uuid4())

        srt_logger.info(f"生成SRT文件请求 - IP: {client_ip}, User-Agent: {user_agent}, Log-ID: {log_id}")
        srt_logger.debug(f"HTML内容长度: {len(html_text)} 字符")

        if not html_text:
            srt_logger.error("未提供HTML文本内容")
            return jsonify({'status': 'error', 'message': 'No html_text provided'})

        # 记录表格操作
        table_operations = data.get('table_operations', [])
        if table_operations:
            srt_logger.info(f"表格操作记录 - Log-ID: {log_id}")
            for op in table_operations:
                srt_logger.info(f"操作类型: {op.get('type')}, 行号: {op.get('row')}, 列: {op.get('column')}, 值: {op.get('value')}")

        table_html = generate_srt_files_and_table(html_text)
        srt_logger.info(f"SRT文件生成成功 - Log-ID: {log_id}, 表格行数: {table_html.count('<tr>')}")
        srt_logger.debug(f"生成的表格HTML长度: {len(table_html)} 字符")

        return jsonify({
            'status': 'success',
            'table_html': table_html,
            'log_id': log_id
        })
    except Exception as e:
        srt_logger.error(f"生成SRT文件失败: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/srt_temp/<filename>')
def serve_srt_file(filename):
    """提供srt_temp目录下的SRT文件，纯文本格式"""
    try:
        srt_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'srt_temp')
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr

        srt_logger.info(f"SRT文件下载请求 - 文件: {filename}, IP: {client_ip}, User-Agent: {user_agent}")

        if not os.path.exists(os.path.join(srt_dir, filename)):
            srt_logger.error(f"SRT文件不存在: {filename}")
            return "File not found", 404

        srt_logger.info(f"SRT文件下载成功: {filename}")
        return send_from_directory(srt_dir, filename, mimetype='text/plain', as_attachment=False)
    except Exception as e:
        srt_logger.error(f"SRT文件下载失败: {str(e)}", exc_info=True)
        return str(e), 500

@app.route('/preview_voice/<filename>')
def serve_voice_file(filename):
    """提供preview_voice目录下的音频文件"""
    try:
        voice_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'preview_voice')
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr

        speech_logger.info(f"音频文件预览请求 - 文件: {filename}, IP: {client_ip}, User-Agent: {user_agent}")

        if not os.path.exists(os.path.join(voice_dir, filename)):
            speech_logger.error(f"音频文件不存在: {filename}")
            return "File not found", 404

        speech_logger.info(f"音频文件预览成功: {filename}")
        return send_from_directory(voice_dir, filename, mimetype='audio/mpeg')
    except Exception as e:
        speech_logger.error(f"音频文件预览失败: {str(e)}", exc_info=True)
        return str(e), 500

@app.route('/test')
def test():
    return "Server is working!"

@app.route('/audio_test')
def audio_test():
    """音频测试页面，支持传入voice_id和filename参数"""
    voice_id = request.args.get('voice_id', VOICE_OPTIONS[0][0])  # 默认使用第一个声音选项
    filename = request.args.get('filename', '')  # 音频文件名

    # 构建音频文件URL
    audio_url = f'/preview_voice/{filename}' if filename else ''

    # 获取当前声音选项的名称
    voice_name = next((name for id, name in VOICE_OPTIONS if id == voice_id), VOICE_OPTIONS[0][1])

    return render_template('audio_test.html',
                         voice_id=voice_id,
                         voice_name=voice_name,
                         audio_url=audio_url)

@app.route('/templates/<template_name>')
def serve_template(template_name):
    """提供模板HTML文件的静态访问"""
    return send_from_directory('templates', template_name)

@app.route('/save_srt', methods=['POST'])
def save_srt():
    """保存修改后的SRT文件内容"""
    try:
        data = request.json
        filename = data.get('filename')
        content = data.get('content')

        # 安全检查
        if not filename or not content:
            return jsonify({'status': 'error', 'message': '文件名或内容不能为空'})

        # 防止路径遍历
        if '..' in filename or filename.startswith('/'):
            return jsonify({'status': 'error', 'message': '无效的文件名'})

        # 确保只保存到srt_temp目录下的srt文件
        if not filename.endswith('.srt'):
            return jsonify({'status': 'error', 'message': '只能保存SRT文件'})

        srt_path = os.path.join('srt_temp', filename)

        # 记录日志
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr
        srt_logger.info(f"保存SRT文件 - 文件名: {filename}, IP: {client_ip}, User-Agent: {user_agent}")

        # 写入文件
        with open(srt_path, 'w', encoding='utf-8') as f:
            f.write(content)

        srt_logger.info(f"SRT文件保存成功 - 文件名: {filename}, 内容长度: {len(content)}")
        return jsonify({'status': 'success'})

    except Exception as e:
        srt_logger.error(f"保存SRT文件失败: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/generate_audio', methods=['POST'])
def generate_audio():
    """从SRT文件生成音频文件"""
    try:
        from voice_create import generate_audio_from_srt

        data = request.json
        filename = data.get('filename')
        voice_id = data.get('voice_id')

        # 安全检查
        if not filename or not voice_id:
            return jsonify({'status': 'error', 'message': '文件名或音色ID不能为空'})

        # 防止路径遍历
        if '..' in filename or filename.startswith('/'):
            return jsonify({'status': 'error', 'message': '无效的文件名'})

        # 确保只处理srt_temp目录下的srt文件
        if not filename.endswith('.srt'):
            return jsonify({'status': 'error', 'message': '只能处理SRT文件'})

        srt_path = os.path.join('srt_temp', filename)
        if not os.path.exists(srt_path):
            return jsonify({'status': 'error', 'message': 'SRT文件不存在'})

        # 确保created_voice目录存在
        created_voice_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "created_voice")
        if not os.path.exists(created_voice_dir):
            os.makedirs(created_voice_dir)
            logging.info(f"Created voice directory: {created_voice_dir}")

        # 记录日志
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr
        speech_logger.info(f"生成音频请求 - 文件名: {filename}, 音色ID: {voice_id}, IP: {client_ip}, User-Agent: {user_agent}")

        # 确保音色ID是字符串类型
        voice_id_str = str(voice_id)
        speech_logger.info(f"转换后的音色ID: {voice_id_str}")

        # 生成音频文件
        generated_files = generate_audio_from_srt(srt_path, voice_id_str, created_voice_dir)

        if not generated_files:
            return jsonify({'status': 'error', 'message': '音频生成失败'})

        speech_logger.info(f"音频生成成功 - 文件名: {filename}, 生成文件数: {len(generated_files)}")
        return jsonify({
            'status': 'success',
            'files': generated_files,
            'message': f'成功生成{len(generated_files)}个音频文件'
        })

    except Exception as e:
        speech_logger.error(f"生成音频失败: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/merge_audio', methods=['POST'])
def merge_audio():
    """合并SRT文件对应的所有音频文件"""
    try:
        from audio_merge import merge_audio_files

        data = request.json
        filename = data.get('filename')

        # 安全检查
        if not filename:
            return jsonify({'status': 'error', 'message': '文件名不能为空'})

        # 防止路径遍历
        if '..' in filename or filename.startswith('/'):
            return jsonify({'status': 'error', 'message': '无效的文件名'})

        # 确保只处理srt_temp目录下的srt文件
        if not filename.endswith('.srt'):
            return jsonify({'status': 'error', 'message': '只能处理SRT文件'})

        srt_path = os.path.join('srt_temp', filename)
        if not os.path.exists(srt_path):
            return jsonify({'status': 'error', 'message': 'SRT文件不存在'})

        # 确保created_voice目录存在
        created_voice_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "created_voice")
        if not os.path.exists(created_voice_dir):
            os.makedirs(created_voice_dir)
            logging.info(f"Created voice directory: {created_voice_dir}")

        # 记录日志
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr
        speech_logger.info(f"合并音频请求 - 文件名: {filename}, IP: {client_ip}, User-Agent: {user_agent}")

        # 合并音频文件
        merged_file = merge_audio_files(srt_path, created_voice_dir)

        if not merged_file:
            return jsonify({'status': 'error', 'message': '音频合并失败'})

        speech_logger.info(f"音频合并成功 - 文件名: {filename}, 合并文件: {merged_file}")
        return jsonify({
            'status': 'success',
            'file': merged_file,
            'message': '音频合并成功'
        })

    except Exception as e:
        speech_logger.error(f"合并音频失败: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/created_voice/<filename>')
def serve_created_voice_file(filename):
    """提供created_voice目录下的音频文件"""
    try:
        voice_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'created_voice')
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr

        speech_logger.info(f"生成音频文件请求 - 文件: {filename}, IP: {client_ip}, User-Agent: {user_agent}")

        if not os.path.exists(os.path.join(voice_dir, filename)):
            speech_logger.error(f"生成音频文件不存在: {filename}")
            return "File not found", 404

        speech_logger.info(f"生成音频文件访问成功: {filename}")
        return send_from_directory(voice_dir, filename, mimetype='audio/mpeg')
    except Exception as e:
        speech_logger.error(f"生成音频文件访问失败: {str(e)}", exc_info=True)
        return str(e), 500

# 数据保存相关API路由
# 项目保存、加载和管理相关的API路由函数已移除

if __name__ == '__main__':
    print("[启动] 正在启动Flask应用...")
    print(f"[配置] 使用API密钥数量: {len(API_KEYS)}")
    print(f"[配置] 默认模型: {GEMINI_FLASH_MODEL}")
    print(f"[配置] 图像模型: {GEMINI_IMAGE_MODEL}")
    
    # 检查数据保存功能
    if data_saver:
        print("[功能] 数据保存功能已启用")
    else:
        print("[警告] 数据保存功能不可用")
    
    # 启动时简化API密钥检查（仅检查第一个密钥确保基本可用性）
    print("[启动] 正在进行基础API密钥检查...")
    if API_KEYS:
        first_key_result = check_llm_interface(API_KEYS[0])
        if first_key_result["status"] == "success":
            print(f"[启动] 基础API密钥检查通过")
        elif first_key_result["status"] == "warning":
            print(f"[启动] 网络连接问题: {first_key_result['message']}")
            print("[启动] 程序将继续启动，LLM功能可能受限")
        elif first_key_result["status"] == "interrupted":
            print("[启动] 用户中断了API检查")
            print("[启动] 程序将继续启动")
        else:
            print(f"[启动] 基础API密钥检查失败: {first_key_result['message']}")
            print("[启动] 将在后台定时检查中验证所有密钥")
    else:
        print("[错误] 没有配置任何API密钥")
        exit(1)
    
    # 启动后台定时检查线程
    print("[启动] 启动API密钥定时检查线程（每小时检查一次）...")
    api_check_thread = threading.Thread(target=periodic_api_key_check, daemon=True)
    api_check_thread.start()
    print("[启动] API密钥定时检查线程已启动")
    
    # 设置初始检查时间戳为当前时间，1小时后进行第一次完整检查
    last_api_check_time = time.time()
    
    print("[启动] 服务器启动中...")
    app.run(debug=True, host='0.0.0.0', port=5000)




