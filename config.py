API_KEYS = [
    "AIzaSyCHq2lftMCDOkYwh_HAqdfOp7a1bngmLYY",
    "AIzaSyCtDGGEpqdHAUb0FxSa-646WwKcA9s5SgE",
    "AIzaSyALbCkBZWd7DR6r--bqGAo34raFwQI750I",
    "AIzaSyD-szdfuJwsWORqxLXURukB8GGLChTYXtM",
    "AIzaSyDsX_wib_Hy3I03JzoQZwJg2YpRpNA-3T8",
    "AIzaSyC9Ti5pkzntnUp03l8LrB2PtUKMoRbIA1U",
    "AIzaSyBY9NPXv_sWsd7XRNDawA0mSpVBB5owsHs"
]
#    "AIzaSyCHq2lftMCDOkYwh_HAqdfOp7a1bngmLYY",

# 模型配置统一管理
#MODEL_NAME = "gemini-2.0-flash-exp-image-generation"
#GEMINI_FLASH_MODEL = "gemini-2.0-flash"
#GEMINI_FLASH_MODEL = "gemini-2.0-flash-exp-image-generation"
GEMINI_FLASH_MODEL = "gemini-2.5-flash-preview-04-17"
#GEMINI_FLASH_MODEL = "gemini-2.5-pro-preview-05-06"

# 图像生成模型配置
GEMINI_IMAGE_MODEL = "gemini-2.0-flash-exp-image-generation"

# 提示词模版配置
DEFAULT_PROMPT_TEMPLATE = """
    1、请开始一个新的话题，忘掉之前的资料。
    请生成1~5随机数，按数字选择故事数组分类，请选择{随机人类角色+其他类/随机陆地动物+其他类/随机海洋生物+其他类/随机飞行生物+其他类/随机神话生物+其他类角色}+{随机全球冒险地点/随机历史事件/随机名人故事/随机全球神话故事}冒险题材的，
    剧情曲折反复，让人意想不到的，再按安徒生和格林童话写作风格，叙事手法等，构思一个儿童绘本故事，
    符合6~13岁少年儿童特点和认知，500字左右，共8页。每次选定一个主题展开故事（中心思想），配上一个小红书式吸引人的标题。
    返回的消息模版如下，不要遗漏任何内容，也不要增加其他内容：
    标题：《XXX》
    第X页：
    主持人角色对白：<页面内容概述>
    主持人角色配音要求：<全篇统一为某个音色，其特色为：成熟稳重、有说服力。>
    XXXXX角色对白：<XXX。>
    XXXXX角色配音要求：<对配音的年龄，男女声，配音特色要求。>
    注意：以上的XXX角色对白和XXX角色配音要求成对出现。
    每页包含场景配图，生图Prompt详细描述： <生图Prompt详细描述详细内容>
    注意：
    第一，参考stable diffusion用中文表述，描述需要包含角色和它的主要（外形外貌衣着服饰），包括，动作，表情，以及周边详细环境，镜头，氛围等。
    第二，参考格式如下： 【图片风格为「3D渲染」，迪士尼卡通，色彩鲜艳，七彩气泡，松鼠系着皮质围裙，
    在空心树干内整理刻满年轮的木质档案，前爪举着橡果放大镜，树洞外飘落银杏叶日历，中景，特写，超精细，大师构图，
    背景虚幻，比例 "16:9"】。
    第三，其中注意，图片风格为「3D渲染」，色彩鲜艳，生动活泼，为每个生图prompt必须有的关键字，
    一开始就要根据场景加入主体，加入中景，全景，特写，微距，鱼眼等镜头，加入俯视，仰视等视角，按情况添加到主体关键字。
    第四，其中注意，专业光影效果，超精细，大师构图，背景虚幻，比例 "16:9"。为每个生图prompt必须有的关键字。
    2、请直接输出返回内容，不要任何回复。这是返回消息的其中一页完整模版：
    <
    第1页：

    主持人角色对白：小兔子跳跳和小刺猬滚滚XXX！

    主持人角色配音要求：<全篇统一为某个音色，XXX。>

    小兔子跳跳角色对白：<哇！海底的宝藏！XXX？>

    小兔子跳跳角色配音要求：<儿童女声，XXX。>

    小刺猬滚滚角色对白：<也许吧，XXX！>

    小刺猬滚滚角色配音要求：<儿童男声，XXX。>

    生图Prompt详细描述： 【图片风格为「3D渲染」，XXX，比例 "16:9"】
"""

# 运镜提示词配置模板（基于即梦3.0运镜功能全解表）
CAMERA_PROMPT_CONFIG = {
    # 基础要求配置
    "basic_requirements": {
        "shot_count": "2~3个连贯的分镜",
        "structure": "景别，视角，运镜，画面内容",
        "processing_note": "LLM消息先智能处理\"生图提示词\"，并取出中间描述故事内容的部分后，返回LLM消息内容",
        "transition": "镜头切换固定硬切",
        "word_limit": "用词精练不能起过300字",
        "output_instruction": "请直接输出返回内容，不要任何回复"
    },
    
    # 即梦3.0运镜功能全解表 - 基础运镜
    "basic_movements": {
        "zoom_in": {
            "name": "推镜头(Zoom In)",
            "tech_essence": "镜头物理靠近主体",
            "core_function": "突出细节/制造压迫感",
            "classic_scenes": "广告产品特写、人物情绪转折",
            "operation_formula": "镜头从A推到B，聚焦[细节]，时长X秒"
        },
        "zoom_out": {
            "name": "拉镜头(Zoom Out)",
            "tech_essence": "镜头物理远离主体",
            "core_function": "展现场景全貌/收尾",
            "classic_scenes": "纪录片环境全景、电影结尾",
            "operation_formula": "从C特写匀速拉远，展现[D场景]"
        },
        "pan": {
            "name": "摇镜头(Pan)",
            "tech_essence": "三脚架式水平/垂直转动",
            "core_function": "横向扫描环境",
            "classic_scenes": "历史片建筑群、集市全貌",
            "operation_formula": "左→右摇180°，速度X，拍[场景]"
        },
        "track": {
            "name": "移镜头(Track)",
            "tech_essence": "沿轨迹滑行拍摄",
            "core_function": "动态探索空间",
            "classic_scenes": "旅游vlog街景、追逐戏跟拍",
            "operation_formula": "沿[路径]移动，速度X，拍[内容]"
        },
        "follow": {
            "name": "跟镜头(Follow)",
            "tech_essence": "与运动主体同步移动",
            "core_function": "强化动作连贯性",
            "classic_scenes": "体育赛事、武侠打斗",
            "operation_formula": "跟拍[主体]，保持距离X米"
        },
        "rise": {
            "name": "升镜头(Rise)",
            "tech_essence": "垂直向上运动",
            "core_function": "打造宏大感",
            "classic_scenes": "城市宣传片开场",
            "operation_formula": "地面升到X米，展现[全景]"
        },
        "descend": {
            "name": "降镜头(Descend)",
            "tech_essence": "垂直向下运动",
            "core_function": "聚焦微观/制造悬念",
            "classic_scenes": "悬疑片揭晓真相",
            "operation_formula": "高空降至[位置]，揭露[关键物]"
        }
    },
    
    # 即梦3.0运镜功能全解表 - 创意运镜
    "creative_movements": {
        "orbital": {
            "name": "环绕运镜(Orbital)",
            "tech_essence": "圆周环绕主体",
            "core_function": "360°展示物体",
            "classic_scenes": "汽车广告、角色亮相",
            "operation_formula": "绕[主体]转X圈，半径X米"
        },
        "counter": {
            "name": "对冲运镜(Counter)",
            "tech_essence": "镜头与主体反向运动",
            "core_function": "制造速度与冲突",
            "classic_scenes": "警匪追车戏、动作片",
            "operation_formula": "镜头前冲 vs [主体]后撤"
        },
        "pov": {
            "name": "POV视角",
            "tech_essence": "第一人称视角",
            "core_function": "沉浸式体验",
            "classic_scenes": "游戏宣传片、探险纪录片",
            "operation_formula": "POV视角：我[动作]+[所见]"
        },
        "peeking": {
            "name": "窥视视角(Peeking)",
            "tech_essence": "遮挡式构图",
            "core_function": "营造神秘感",
            "classic_scenes": "恐怖片、侦探剧",
            "operation_formula": "从[缝隙]窥视[秘密场景]"
        }
    },
    
    # 即梦3.0运镜功能全解表 - 场景强化
    "scene_enhancement": {
        "narrow_space": {
            "name": "狭小空间运镜",
            "tech_essence": "受限空间内运镜",
            "core_function": "突出压迫感",
            "classic_scenes": "电梯惊魂、密室逃脱",
            "operation_formula": "在[小空间]内推/摇展现[压抑感]"
        },
        "special_angle": {
            "name": "特殊视角",
            "tech_essence": "非常规角度拍摄",
            "core_function": "打破视觉惯性",
            "classic_scenes": "英雄仰拍/反派俯拍",
            "operation_formula": "[低/高/斜]角度拍[主体]"
        },
        "fisheye": {
            "name": "鱼眼扭曲(Fisheye)",
            "tech_essence": "边缘拉伸变形",
            "core_function": "制造荒诞感",
            "classic_scenes": "卡通搞笑场景、MV",
            "operation_formula": "鱼眼镜头拍[夸张场景]"
        }
    },
    
    # 即梦3.0运镜功能全解表 - 动态追踪
    "dynamic_tracking": {
        "follow_focus": {
            "name": "跟踪聚焦(Follow Focus)",
            "tech_essence": "动态追焦主体",
            "core_function": "突出运动主体",
            "classic_scenes": "足球赛明星跟拍",
            "operation_formula": "锁定[主体]运动轨迹追焦"
        },
        "car_rig": {
            "name": "车拍滑轨(Car Rig)",
            "tech_essence": "模拟车载移动",
            "core_function": "打造流畅动线",
            "classic_scenes": "汽车广告、公路片",
            "operation_formula": "以X速度沿[路线]车拍"
        }
    },
    
    # 即梦3.0运镜功能全解表 - 光学变形
    "optical_effects": {
        "hitchcock_zoom": {
            "name": "希区柯克变焦",
            "tech_essence": "变焦+位移反向操作",
            "core_function": "空间扭曲眩晕感",
            "classic_scenes": "悬疑片惊恐时刻",
            "operation_formula": "前移镜头+拉长焦距"
        },
        "lens_breathe": {
            "name": "镜头呼吸(Breathe)",
            "tech_essence": "模拟对焦呼吸感",
            "core_function": "增加电影质感",
            "classic_scenes": "人物情绪特写",
            "operation_formula": "添加呼吸效果强度X"
        }
    },
    
    # 即梦3.0运镜功能全解表 - 物理模拟
    "physics_simulation": {
        "liquid_attach": {
            "name": "流体附着(Liquid)",
            "tech_essence": "绑定流体运动轨迹",
            "core_function": "增强自然互动感",
            "classic_scenes": "饮料广告水珠滑落",
            "operation_formula": "镜头贴[水流]轨迹移动"
        },
        "fractal_travel": {
            "name": "分形穿梭(Fractal)",
            "tech_essence": "分形几何通道穿梭",
            "core_function": "打造迷幻视觉",
            "classic_scenes": "科幻片穿越场景",
            "operation_formula": "在[分形结构]中沿Y轴穿梭"
        },
        "micro_tracking": {
            "name": "细胞级追踪(Micro)",
            "tech_essence": "微观细节追踪",
            "core_function": "揭示隐藏世界",
            "classic_scenes": "科普片自然奇观",
            "operation_formula": "追踪[露珠]在[叶片]滚动"
        },
        "macro_travel": {
            "name": "星际穿越(Macro)",
            "tech_essence": "宇宙尺度移动",
            "core_function": "表现浩瀚空间",
            "classic_scenes": "航天纪录片",
            "operation_formula": "从地球飞向[星云]"
        }
    },
    
    # 即梦3.0运镜功能全解表 - 情绪引擎
    "emotion_engine": {
        "heartbeat_shake": {
            "name": "心跳震颤(Heartbeat)",
            "tech_essence": "同步心跳频率震动",
            "core_function": "传递生理级情绪",
            "classic_scenes": "恐怖片/爱情悸动",
            "operation_formula": "添加X频率震动模拟心跳"
        }
    },
    
    # 五大类型影视创作运镜配方
    "film_type_formulas": {
        "commercial_ad": {
            "name": "商业广告（汽车广告案例）",
            "formula": [
                "低角度仰拍（30°）：车身金属反光流动",
                "环绕运镜（半径1.5米）：3圈展示车标雕刻",
                "车拍滑轨（60km/h）：沿海岸公路移动拍摄",
                "对冲运镜：镜头前冲vs汽车迎面驶来"
            ]
        },
        "historical_film": {
            "name": "历史片（战场场景）",
            "formula": [
                "摇镜头（水平180°）：扫描硝烟中破损战旗",
                "移镜头（S型轨迹）：马蹄踏过尸骸跟拍",
                "降镜头（100米→5米）：聚焦将军断剑特写",
                "心跳震颤（120bpm）：中箭瞬间镜头震动"
            ]
        },
        "documentary": {
            "name": "纪录片（故宫探秘）",
            "formula": [
                "窥视视角：透过门缝拍大殿梁枋彩画",
                "升镜头（地面→50米）：太和殿云纹屋顶",
                "细胞级追踪：雨水沿琉璃瓦滴落轨迹",
                "移镜头（沿中轴线）：展现建筑群对称美"
            ]
        },
        "short_drama": {
            "name": "短剧（悬疑片段）",
            "formula": [
                "希区柯克变焦：发现尸体时空间扭曲",
                "POV视角：凶手慢慢逼近的主观镜头",
                "鱼眼扭曲：惊恐面孔变形特写",
                "狭小空间运镜：衣柜缝隙拍凶手翻找"
            ]
        },
        "cartoon": {
            "name": "卡通片（搞笑场景）",
            "formula": [
                "快速推拉：光头强踩香蕉皮脸着地特写",
                "倾斜角度（45°）：熊二偷蜂蜜时镜头歪斜",
                "流体附着：蜂蜜滴落轨迹粘住熊掌",
                "分形穿梭：在蜂巢六边形结构中追逐"
            ]
        }
    },
    
    # 即梦3.0运镜黄金公式
    "golden_formula": {
        "structure": "[主体]在[场景]做[动作] + [运镜类型]（[参数]） + [辅助元素] + [情绪强化]",
        "example": "舞者（主体）在雨林（场景）旋转跳跃（动作） + 环绕运镜（半径2m/速度0.5x） + 雨滴附着丝带（辅助） + 心跳震颤（恐惧时120bpm）"
    },
    
    # 输出格式配置
    "output_format": {
        "shot_format": "景别[1~N]：XXX；[换行]",
        "angle_format": "视角：XXX；[换行]",
        "movement_format": "运镜：描述运镜细节；[换行]",
        "content_format": "画面内容：XXX(要求用词精练，总字数不超过300字。)[换行]"
    },
    
    # 提示词模板构建函数
    "template_instruction": """
    请根据以上运镜参考，思考不同场景下的运镜特效，给观众最舒适的画面体验，为生图提示词设计3~4个连贯的分镜脚本，
    格式如下：
    景别[1~N]：XXX；[换行]
    视角：XXX；[换行]
    运镜：XXX；[换行]
    画面内容：XXX(要求用词精练，总字数不超过300字。)[换行]"""
}

# Chrome浏览器配置数组
# User Data目录和Chrome可执行文件路径成对使用
CHROME_CONFIGS = [
    {
        "user_data_dir": r"E:\MyChrome\Mychrome1\User Data",
        "executable_path": r"E:\MyChrome\Mychrome1\Chrome\chrome.exe"
    },
    {
        "user_data_dir": r"E:\MyChrome\Mychrome2\User Data",
        "executable_path": r"E:\MyChrome\Mychrome2\Chrome\chrome.exe"
    },
    {
        "user_data_dir": r"E:\MyChrome\Mychrome3\User Data",
        "executable_path": r"E:\MyChrome\Mychrome3\Chrome\chrome.exe"
    },    
    {
        "user_data_dir": r"E:\MyChrome\Mychrome4\User Data",
        "executable_path": r"E:\MyChrome\Mychrome4\Chrome\chrome.exe"
    },
    {
        "user_data_dir": r"E:\MyChrome\Mychrome5\User Data",
        "executable_path": r"E:\MyChrome\Mychrome5\Chrome\chrome.exe"
    },
    {
        "user_data_dir": r"E:\MyChrome\Mychrome6\User Data",
        "executable_path": r"E:\MyChrome\Mychrome6\Chrome\chrome.exe"
    },    
]

# 运镜提示词构建函数（基于即梦3.0运镜功能全解表）
def build_camera_prompt(prompt_content: str) -> str:
    """
    构建运镜提示词的完整Prompt，基于即梦3.0运镜功能全解表
    
    Args:
        prompt_content (str): 生图提示词内容
        
    Returns:
        str: 完整的运镜提示词Prompt
    """
    config = CAMERA_PROMPT_CONFIG
    
    # 构建基础要求部分
    basic_req = config["basic_requirements"]
    camera_prompt = f"要求有{basic_req['shot_count']}，分镜脚本结构为：{basic_req['structure']}（{basic_req['processing_note']}），{basic_req['transition']}，{basic_req['word_limit']}。{basic_req['output_instruction']}\n\n"
    
    # 添加生图提示词内容
    camera_prompt += f"生图提示词内容：{prompt_content}\n\n"
    
    # 添加即梦3.0运镜黄金公式
    golden_formula = config["golden_formula"]
    camera_prompt += f"即梦3.0运镜黄金公式：{golden_formula['structure']}\n"
    camera_prompt += f"案例：{golden_formula['example']}\n\n"
    
    # 添加基础运镜参考
    camera_prompt += "=== 基础运镜 ===\n"
    basic_movements = config["basic_movements"]
    for key, movement in basic_movements.items():
        camera_prompt += f"{movement['name']}：{movement['tech_essence']}，{movement['core_function']}\n"
        camera_prompt += f"  应用场景：{movement['classic_scenes']}\n"
        camera_prompt += f"  操作口诀：{movement['operation_formula']}\n\n"
    
    # 添加创意运镜参考
    camera_prompt += "=== 创意运镜 ===\n"
    creative_movements = config["creative_movements"]
    for key, movement in creative_movements.items():
        camera_prompt += f"{movement['name']}：{movement['tech_essence']}，{movement['core_function']}\n"
        camera_prompt += f"  应用场景：{movement['classic_scenes']}\n"
        camera_prompt += f"  操作口诀：{movement['operation_formula']}\n\n"
    
    # 添加场景强化运镜参考
    camera_prompt += "=== 场景强化 ===\n"
    scene_enhancement = config["scene_enhancement"]
    for key, movement in scene_enhancement.items():
        camera_prompt += f"{movement['name']}：{movement['tech_essence']}，{movement['core_function']}\n"
        camera_prompt += f"  应用场景：{movement['classic_scenes']}\n"
        camera_prompt += f"  操作口诀：{movement['operation_formula']}\n\n"
    
    # 添加动态追踪运镜参考
    camera_prompt += "=== 动态追踪 ===\n"
    dynamic_tracking = config["dynamic_tracking"]
    for key, movement in dynamic_tracking.items():
        camera_prompt += f"{movement['name']}：{movement['tech_essence']}，{movement['core_function']}\n"
        camera_prompt += f"  应用场景：{movement['classic_scenes']}\n"
        camera_prompt += f"  操作口诀：{movement['operation_formula']}\n\n"
    
    # 添加光学变形运镜参考
    camera_prompt += "=== 光学变形 ===\n"
    optical_effects = config["optical_effects"]
    for key, movement in optical_effects.items():
        camera_prompt += f"{movement['name']}：{movement['tech_essence']}，{movement['core_function']}\n"
        camera_prompt += f"  应用场景：{movement['classic_scenes']}\n"
        camera_prompt += f"  操作口诀：{movement['operation_formula']}\n\n"
    
    # 添加物理模拟运镜参考
    camera_prompt += "=== 物理模拟 ===\n"
    physics_simulation = config["physics_simulation"]
    for key, movement in physics_simulation.items():
        camera_prompt += f"{movement['name']}：{movement['tech_essence']}，{movement['core_function']}\n"
        camera_prompt += f"  应用场景：{movement['classic_scenes']}\n"
        camera_prompt += f"  操作口诀：{movement['operation_formula']}\n\n"
    
    # 添加情绪引擎运镜参考
    camera_prompt += "=== 情绪引擎 ===\n"
    emotion_engine = config["emotion_engine"]
    for key, movement in emotion_engine.items():
        camera_prompt += f"{movement['name']}：{movement['tech_essence']}，{movement['core_function']}\n"
        camera_prompt += f"  应用场景：{movement['classic_scenes']}\n"
        camera_prompt += f"  操作口诀：{movement['operation_formula']}\n\n"
    
    # 添加五大类型影视创作运镜配方
    camera_prompt += "=== 五大类型影视创作运镜配方 ===\n"
    film_formulas = config["film_type_formulas"]
    for key, formula_type in film_formulas.items():
        camera_prompt += f"{formula_type['name']}：\n"
        for i, formula in enumerate(formula_type['formula'], 1):
            camera_prompt += f"  {i}. {formula}\n"
        camera_prompt += "\n"
    
    # 添加模板指令
    camera_prompt += "请根据以上运镜参考，思考不同场景下的运镜特效，给观众最舒适的画面体验，为生图提示词设计3~4个连贯的分镜脚本，\n"
    camera_prompt += "格式如下：\n"
    output_format = config["output_format"]
    camera_prompt += f"{output_format['shot_format']}"
    camera_prompt += f"{output_format['angle_format']}"
    camera_prompt += f"{output_format['movement_format']}"
    camera_prompt += f"{output_format['content_format']}"
    
    return camera_prompt
